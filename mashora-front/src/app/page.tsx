"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { OptimizedImage } from "@/components/optimized-image";
import { ThemeToggle } from "@/components/theme-toggle";
import {
  Heart,
  Users,
  BookOpen,
  Award,
  Clock,
  CheckCircle,
  Star,
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  ChevronDown,
  Play,
  Download,
  Share2,
  User,
  Globe,
} from "lucide-react";

// SEO Component
function SEOHead() {
  return (
    <>
      {/* Primary Meta Tags */}
      <title>كورس الإعداد للزواج - أبرشية شبين القناطر | مشورة</title>
      <meta
        name="title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر | مشورة"
      />
      <meta
        name="description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي. برنامج معتمد من أبرشية شبين القناطر."
      />
      <meta
        name="keywords"
        content="زواج مسيحي, إعداد للزواج, كورس زواج, مشورة زواج, أبرشية شبين القناطر, الكنيسة القبطية, تأهيل للزواج, حياة زوجية سعيدة"
      />
      <meta name="robots" content="index, follow" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="Arabic" />
      <meta name="author" content="أبرشية شبين القناطر" />
      <meta name="copyright" content="أبرشية شبين القناطر" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://mashora.diocese-shibin.org/" />
      <meta
        property="og:title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر"
      />
      <meta
        property="og:description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي"
      />
      <meta
        property="og:image"
        content="https://mashora.diocese-shibin.org/og-image.jpg"
      />
      <meta property="og:site_name" content="مشورة - أبرشية شبين القناطر" />
      <meta property="og:locale" content="ar_EG" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta
        property="twitter:url"
        content="https://mashora.diocese-shibin.org/"
      />
      <meta
        property="twitter:title"
        content="كورس الإعداد للزواج - أبرشية شبين القناطر"
      />
      <meta
        property="twitter:description"
        content="استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته عن الزواج المسيحي"
      />
      <meta
        property="twitter:image"
        content="https://mashora.diocese-shibin.org/og-image.jpg"
      />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#f59e0b" />
      <meta name="msapplication-TileColor" content="#f59e0b" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="format-detection" content="telephone=no" />

      {/* Canonical and Alternate */}
      <link rel="canonical" href="https://mashora.diocese-shibin.org/" />
      <link
        rel="alternate"
        hrefLang="ar"
        href="https://mashora.diocese-shibin.org/"
      />
      <link
        rel="alternate"
        hrefLang="en"
        href="https://mashora.diocese-shibin.org/en"
      />

      {/* Preconnect for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossOrigin="anonymous"
      />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@graph": [
              {
                "@type": "Course",
                name: "كورس الإعداد للزواج",
                description:
                  "كورس شامل للإعداد للزواج المسيحي يغطي جميع جوانب الحياة الزوجية",
                provider: {
                  "@type": "Organization",
                  name: "أبرشية شبين القناطر",
                  url: "https://diocese-shibin.org",
                },
                courseMode: "online",
                educationalLevel: "Beginner",
                inLanguage: "ar",
                offers: {
                  "@type": "Offer",
                  price: "0",
                  priceCurrency: "EGP",
                  availability: "https://schema.org/InStock",
                },
                aggregateRating: {
                  "@type": "AggregateRating",
                  ratingValue: "4.9",
                  reviewCount: "150",
                },
              },
              {
                "@type": "Organization",
                name: "أبرشية شبين القناطر",
                url: "https://diocese-shibin.org",
                logo: "https://mashora.diocese-shibin.org/diocese-logo.png",
                contactPoint: {
                  "@type": "ContactPoint",
                  telephone: "+20-13-123-4567",
                  contactType: "customer service",
                  availableLanguage: ["Arabic", "English"],
                },
                address: {
                  "@type": "PostalAddress",
                  streetAddress: "شارع الكنيسة",
                  addressLocality: "شبين القناطر",
                  addressRegion: "القليوبية",
                  addressCountry: "EG",
                },
              },
              {
                "@type": "WebSite",
                url: "https://mashora.diocese-shibin.org",
                name: "مشورة - كورس الإعداد للزواج",
                potentialAction: {
                  "@type": "SearchAction",
                  target:
                    "https://mashora.diocese-shibin.org/search?q={search_term_string}",
                  "query-input": "required name=search_term_string",
                },
              },
              {
                "@type": "WebPage",
                url: "https://mashora.diocese-shibin.org",
                name: "الصفحة الرئيسية - كورس الإعداد للزواج",
                description:
                  "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
                inLanguage: "ar",
                isPartOf: {
                  "@type": "WebSite",
                  url: "https://mashora.diocese-shibin.org",
                },
              },
            ],
          }),
        }}
      />
    </>
  );
}

export default function HomePage() {
  return (
    <>
      <SEOHead />
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        {/* Floating Background Elements - Optimized */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-amber-400/20 to-orange-500/20 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-40 left-20 w-40 h-40 bg-gradient-to-br from-yellow-400/20 to-amber-500/20 rounded-full blur-xl animate-float animation-delay-1000"></div>
        </div>

        {/* Header */}
        <header className="relative z-10 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-amber-200/50 dark:border-slate-700/50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <OptimizedImage
                  src="/diocese-logo.png"
                  alt="شعار أبرشية شبين القناطر"
                  width={50}
                  height={50}
                  className="rounded-full"
                />
                <div>
                  <h1 className="text-xl font-bold text-slate-800 dark:text-white">
                    مشورة
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    أبرشية شبين القناطر
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <ThemeToggle />
                <Button asChild variant="outline" size="sm">
                  <Link href="/signin">تسجيل الدخول</Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 px-4 text-center animate-fade-in-up">
          <div className="container mx-auto max-w-4xl">
            <div className="mb-8">
              <Badge
                variant="secondary"
                className="mb-4 text-sm px-4 py-2 bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
              >
                <Award className="w-4 h-4 ml-2" />
                كورس معتمد من الأبرشية
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 gradient-text py-2.5">
                كورس الإعداد للزواج
              </h1>
              <p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
                استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما
                تحتاج معرفته
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white px-8 py-3 text-lg"
              >
                <Link href="/signup">
                  <Heart className="w-5 h-5 ml-2" />
                  ابدأ رحلتك الآن
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="px-8 py-3 text-lg bg-transparent"
              >
                <Link href="#course-details">
                  <BookOpen className="w-5 h-5 ml-2" />
                  تفاصيل الكورس
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="glass-card">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2">
                    500+
                  </div>
                  <div className="text-slate-600 dark:text-slate-300">
                    زوج وزوجة
                  </div>
                </CardContent>
              </Card>
              <Card className="glass-card">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2">
                    12
                  </div>
                  <div className="text-slate-600 dark:text-slate-300">
                    محاضرة شاملة
                  </div>
                </CardContent>
              </Card>
              <Card className="glass-card">
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2">
                    98%
                  </div>
                  <div className="text-slate-600 dark:text-slate-300">
                    معدل الرضا
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Course Details Section */}
        <section
          id="course-details"
          className="py-20 px-4 animate-fade-in-up animation-delay-400"
        >
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800 dark:text-white">
                ماذا ستتعلم في هذا الكورس؟
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
                كورس شامل يغطي جميع جوانب الحياة الزوجية من منظور مسيحي أصيل
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: Heart,
                  title: "أسس الحب المسيحي",
                  description:
                    "فهم معنى الحب الحقيقي وكيفية بنائه وتنميته في الزواج",
                },
                {
                  icon: Users,
                  title: "التواصل الفعال",
                  description:
                    "تعلم مهارات التواصل والحوار البناء مع شريك الحياة",
                },
                {
                  icon: BookOpen,
                  title: "الأسس الكتابية",
                  description: "دراسة ما يقوله الكتاب المقدس عن الزواج والأسرة",
                },
                {
                  icon: Award,
                  title: "إدارة الصراعات",
                  description: "كيفية التعامل مع الخلافات وحلها بطريقة مسيحية",
                },
                {
                  icon: Clock,
                  title: "إدارة الوقت",
                  description: "تنظيم الوقت بين العمل والأسرة والخدمة",
                },
                {
                  icon: CheckCircle,
                  title: "التخطيط المالي",
                  description: "إدارة الأموال والتخطيط المالي للأسرة المسيحية",
                },
              ].map((item, index) => (
                <Card
                  key={index}
                  className="glass-card hover:shadow-lg transition-all duration-300"
                >
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center mb-4">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl text-slate-800 dark:text-white">
                      {item.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-slate-600 dark:text-slate-300 leading-relaxed">
                      {item.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section - Optimized */}
        <section className="py-20 px-4 bg-white/50 dark:bg-slate-800/50 animate-fade-in-up animation-delay-600">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800 dark:text-white">
                لماذا تختار كورسنا؟
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
                مميزات فريدة تجعل تجربة التعلم أكثر فعالية وإثراءً
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                {[
                  {
                    icon: Play,
                    title: "محاضرات تفاعلية",
                    description:
                      "محاضرات مصورة عالية الجودة مع إمكانية التفاعل والأسئلة",
                  },
                  {
                    icon: Download,
                    title: "مواد قابلة للتحميل",
                    description:
                      "كتيبات ومراجع يمكن تحميلها والرجوع إليها في أي وقت",
                  },
                  {
                    icon: Award,
                    title: "شهادة معتمدة",
                    description:
                      "احصل على شهادة معتمدة من الأبرشية عند إتمام الكورس",
                  },
                  {
                    icon: MessageCircle,
                    title: "دعم مستمر",
                    description:
                      "فريق من المرشدين المتخصصين لمساعدتك طوال الرحلة",
                  },
                ].map((benefit, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <benefit.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-slate-800 dark:text-white">
                        {benefit.title}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-amber-100 to-orange-100 dark:from-slate-700 dark:to-slate-600 rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Play className="w-10 h-10 text-white" />
                    </div>
                    <p className="text-slate-600 dark:text-slate-300 text-lg">
                      شاهد مقدمة الكورس
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 px-4 animate-fade-in-up animation-delay-800">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800 dark:text-white">
                ماذا يقول المشاركون؟
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
                شهادات حقيقية من أزواج استفادوا من الكورس
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "باسم ومريم",
                  text: "كورس رائع ساعدنا كثيراً في فهم أسس الزواج المسيحي. المحاضرات واضحة والمحتوى مفيد جداً.",
                  rating: 5,
                },
                {
                  name: "مينا وسارة",
                  text: "تعلمنا مهارات التواصل والحوار البناء. الكورس غير نظرتنا للزواج بشكل إيجابي.",
                  rating: 5,
                },
                {
                  name: "جورج ونانسي",
                  text: "محتوى غني ومفيد، والشهادة المعتمدة كانت مهمة بالنسبة لنا. ننصح به بشدة.",
                  rating: 5,
                },
              ].map((testimonial, index) => (
                <Card key={index} className="glass-card">
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-5 h-5 fill-amber-400 text-amber-400"
                        />
                      ))}
                    </div>
                    <CardTitle className="text-lg text-slate-800 dark:text-white">
                      {testimonial.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                      &quot;{testimonial.text}&quot;
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section - Optimized */}
        <section className="py-20 px-4 bg-white/50 dark:bg-slate-800/50 animate-fade-in-up animation-delay-1000">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800 dark:text-white">
                الأسئلة الشائعة
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300">
                إجابات على أكثر الأسئلة شيوعاً حول الكورس
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "كم تستغرق مدة الكورس؟",
                  answer:
                    "الكورس يستغرق 6 أسابيع بمعدل محاضرتين أسبوعياً، مع إمكانية التعلم بالسرعة التي تناسبك.",
                },
                {
                  question: "هل الكورس مجاني؟",
                  answer:
                    "نعم، الكورس مجاني تماماً كخدمة من الأبرشية للمقبلين على الزواج.",
                },
                {
                  question: "هل أحتاج لحضور محاضرات مباشرة؟",
                  answer:
                    "لا، جميع المحاضرات مسجلة ويمكنك مشاهدتها في أي وقت يناسبك.",
                },
                {
                  question: "كيف أحصل على الشهادة؟",
                  answer:
                    "بعد إتمام جميع المحاضرات واجتياز الاختبار النهائي، ستحصل على شهادة معتمدة من الأبرشية.",
                },
              ].map((faq, index) => (
                <Card key={index} className="glass-card">
                  <CardHeader>
                    <CardTitle className="text-xl text-slate-800 dark:text-white flex items-center gap-2">
                      <ChevronDown className="w-5 h-5 text-amber-500" />
                      {faq.question}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section - Optimized */}
        <section className="py-20 px-4 animate-fade-in-up animation-delay-1200">
          <div className="container mx-auto max-w-4xl text-center">
            <Card className="glass-card p-12">
              <div className="mb-8">
                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-800 dark:text-white">
                  ابدأ رحلتك نحو زواج سعيد
                </h2>
                <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
                  انضم إلى مئات الأزواج الذين استفادوا من كورسنا وبناوا حياة
                  زوجية مستقرة وسعيدة
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white px-8 py-3 text-lg"
                >
                  <Link href="/signup">
                    <Heart className="w-5 h-5 ml-2" />
                    سجل الآن مجاناً
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="px-8 py-3 text-lg bg-transparent"
                >
                  <Link href="/signin">
                    <User className="w-5 h-5 ml-2" />
                    لديك حساب؟ ادخل
                  </Link>
                </Button>
              </div>

              <div className="mt-8 flex items-center justify-center gap-6 text-sm text-slate-500 dark:text-slate-400">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  شهادة معتمدة
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  دعم مستمر
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-slate-900 dark:bg-slate-950 text-white py-16 px-4">
          <div className="container mx-auto max-w-6xl">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {/* Logo and Description */}
              <div className="lg:col-span-2">
                <div className="flex items-center gap-4 mb-6">
                  <OptimizedImage
                    src="/diocese-logo.png"
                    alt="شعار أبرشية شبين القناطر"
                    width={50}
                    height={50}
                    className="rounded-full"
                  />
                  <div>
                    <h3 className="text-2xl font-bold">مشورة</h3>
                    <p className="text-slate-300">أبرشية شبين القناطر</p>
                  </div>
                </div>
                <p className="text-slate-300 leading-relaxed mb-6">
                  نساعدك في الاستعداد لحياة زوجية سعيدة ومستقرة من خلال كورس
                  شامل يغطي جميع جوانب الزواج المسيحي.
                </p>
                <div className="flex gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-slate-600 text-slate-300 hover:bg-slate-800 bg-transparent"
                  >
                    <Share2 className="w-4 h-4 ml-2" />
                    شارك
                  </Button>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-lg font-semibold mb-4">روابط سريعة</h4>
                <ul className="space-y-2">
                  <li>
                    <Link
                      href="/signup"
                      className="text-slate-300 hover:text-slate-100 transition-colors"
                    >
                      التسجيل
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/signin"
                      className="text-slate-300 hover:text-slate-100 transition-colors"
                    >
                      تسجيل الدخول
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="#course-details"
                      className="text-slate-300 hover:text-slate-100 transition-colors"
                    >
                      تفاصيل الكورس
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/certificate"
                      className="text-slate-300 hover:text-slate-100 transition-colors"
                    >
                      الشهادات
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="text-lg font-semibold mb-4">تواصل معنا</h4>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-300">+20-13-123-4567</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-300">
                      <EMAIL>
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-300">
                      شبين القناطر، القليوبية
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Globe className="w-4 h-4 text-amber-400" />
                    <span className="text-slate-300">diocese-shibin.org</span>
                  </li>
                </ul>
              </div>
            </div>

            <Separator className="bg-slate-700 mb-8" />

            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-slate-400 text-sm">
                © 2024 أبرشية شبين القناطر. جميع الحقوق محفوظة.
              </p>
              <div className="flex items-center gap-4 text-sm text-slate-400">
                <Link
                  href="/privacy"
                  className="hover:text-white transition-colors"
                >
                  سياسة الخصوصية
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-white transition-colors"
                >
                  شروط الاستخدام
                </Link>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
