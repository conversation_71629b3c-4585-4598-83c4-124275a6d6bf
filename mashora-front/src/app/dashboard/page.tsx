"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  Play,
  Lock,
  CheckCircle,
  Download,
  FileText,
  LogOut,
  Menu,
  X,
  Award,
  Clock,
  BookOpen,
  Star,
  ChevronLeft,
  Home,
  Settings,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock user data - in real app this would come from API/database
const mockUser = {
  firstName: "أبانوب",
  lastName: "نشأت",
  email: "<EMAIL>",
  avatar: "/placeholder.svg?height=80&width=80",
  overallProgress: 75,
  currentLecture: 6,
  completedLectures: [1, 2, 3, 4, 5],
  hasPassedFinalExam: false,
}

// Mock lectures data
const mockLectures = [
  {
    id: 1,
    number: "٠١",
    title: "مقدمة عن سر الزيجة",
    titleEn: "Introduction to the Sacrament of Marriage",
    duration: "45 دقيقة",
    description: "فهم المعنى الروحي والكتابي لسر الزيجة في المسيحية",
  },
  {
    id: 2,
    number: "٠٢",
    title: "التواصل الفعال بين الزوجين",
    titleEn: "Effective Communication Between Spouses",
    duration: "50 دقيقة",
    description: "تعلم مهارات التواصل الصحي وحل النزاعات",
  },
  {
    id: 3,
    number: "٠٣",
    title: "إدارة الأموال في الحياة الزوجية",
    titleEn: "Financial Management in Married Life",
    duration: "40 دقيقة",
    description: "التخطيط المالي والميزانية للأسرة الجديدة",
  },
  {
    id: 4,
    number: "٠٤",
    title: "العلاقة الحميمة والاحترام المتبادل",
    titleEn: "Intimacy and Mutual Respect",
    duration: "55 دقيقة",
    description: "بناء علاقة صحية قائمة على الحب والاحترام",
  },
  {
    id: 5,
    number: "٠٥",
    title: "التعامل مع الأهل والعائلة",
    titleEn: "Dealing with In-Laws and Family",
    duration: "35 دقيقة",
    description: "إقامة حدود صحية مع الأهل والعائلة الممتدة",
  },
  {
    id: 6,
    number: "٠٦",
    title: "التخطيط للمستقبل والأطفال",
    titleEn: "Planning for the Future and Children",
    duration: "45 دقيقة",
    description: "التحضير لاستقبال الأطفال والتربية المسيحية",
  },
  {
    id: 7,
    number: "٠٧",
    title: "الصلاة والحياة الروحية المشتركة",
    titleEn: "Prayer and Shared Spiritual Life",
    duration: "30 دقيقة",
    description: "بناء حياة روحية قوية كزوجين مسيحيين",
  },
  {
    id: 8,
    number: "٠٨",
    title: "مراجعة شاملة واستعداد للاختبار",
    titleEn: "Comprehensive Review and Exam Preparation",
    duration: "60 دقيقة",
    description: "مراجعة جميع المواضيع والاستعداد للاختبار النهائي",
  },
]

export default function DashboardPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [animatedProgress, setAnimatedProgress] = useState(0)

  // Animate progress bar on load
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(mockUser.overallProgress)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  // Determine lecture state
  const getLectureState = (lectureId: number) => {
    if (mockUser.completedLectures.includes(lectureId)) {
      return "completed"
    } else if (lectureId === 1 || mockUser.completedLectures.includes(lectureId - 1)) {
      return "unlocked"
    } else {
      return "locked"
    }
  }

  // Get smart CTA button content
  const getSmartCTA = () => {
    if (mockUser.overallProgress === 0) {
      return {
        text: "ابدأ الدورة",
        subtext: "Start Course",
        action: () => console.log("Start course"),
        icon: Play,
      }
    } else if (mockUser.overallProgress < 100) {
      return {
        text: "أكمل الدورة",
        subtext: "Continue Course",
        action: () => console.log("Continue course"),
        icon: Play,
      }
    } else if (!mockUser.hasPassedFinalExam) {
      return {
        text: "ابدأ الاختبار النهائي",
        subtext: "Start Final Exam",
        action: () => console.log("Start exam"),
        icon: FileText,
      }
    } else {
      return {
        text: "حمّل شهادتك",
        subtext: "Download Certificate",
        action: () => console.log("Download certificate"),
        icon: Download,
      }
    }
  }

  const smartCTA = getSmartCTA()
  const SmartCTAIcon = smartCTA.icon

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center gap-4">
              <div className="relative w-12 h-12 rounded-xl overflow-hidden">
                <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" priority />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white">مشورة</h1>
                <p className="text-sm text-slate-600 dark:text-slate-400">Mashora Platform</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-blue-600 dark:text-blue-400 font-semibold"
              >
                <Home className="w-4 h-4" />
                لوحة التحكم
              </Link>
              <ThemeToggle />
            </nav>

            {/* Mobile Menu Button */}
            <div className="md:hidden flex items-center gap-3">
              <ThemeToggle />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="text-slate-600 dark:text-slate-400"
              >
                {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside
          className={`fixed inset-y-0 right-0 z-40 w-80 bg-white dark:bg-slate-900 border-l border-slate-200 dark:border-slate-800 transform transition-transform duration-300 ease-in-out ${sidebarOpen ? "translate-x-0" : "translate-x-full"} md:relative md:translate-x-0 md:w-72`}
        >
          <div className="p-6 space-y-6">
            {/* User Profile */}
            <div className="text-center">
              <div className="relative w-20 h-20 mx-auto mb-4">
                <img
                  src={mockUser.avatar || "/placeholder.svg"}
                  alt="صورة المستخدم"
                  className="w-full h-full rounded-full object-cover border-4 border-blue-100 dark:border-blue-900"
                />
                <div className="absolute -bottom-1 -left-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-slate-900"></div>
              </div>
              <h3 className="font-bold text-slate-900 dark:text-white text-lg">
                {mockUser.firstName} {mockUser.lastName}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">{mockUser.email}</p>
            </div>

            {/* Navigation Links */}
            <nav className="space-y-2">
              <Link
                href="/dashboard"
                className="flex items-center gap-3 px-4 py-3 rounded-xl bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 font-semibold"
              >
                <Home className="w-5 h-5" />
                لوحة التحكم
              </Link>

              <Link
                href="/profile"
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
              >
                <Settings className="w-5 h-5" />
                تعديل الملف الشخصي
              </Link>

              <Link
                href="/receipt"
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
              >
                <FileText className="w-5 h-5" />
                تحميل إيصال الدفع
              </Link>

              <Link
                href="/certificate"
                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-colors ${
                  mockUser.hasPassedFinalExam
                    ? "text-amber-600 dark:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-900/20"
                    : "text-slate-400 dark:text-slate-600 cursor-not-allowed"
                }`}
              >
                <Award className="w-5 h-5" />
                تحميل الشهادة
              </Link>

              <button className="flex items-center gap-3 px-4 py-3 rounded-xl text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors w-full text-right">
                <LogOut className="w-5 h-5" />
                تسجيل الخروج
              </button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6 md:p-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/90 to-indigo-600/90"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-3xl font-bold mb-2">أهلاً بك مرة أخرى، {mockUser.firstName}</h2>
                    <p className="text-blue-100 text-lg">Welcome back, {mockUser.firstName}</p>
                  </div>
                  <div className="hidden md:block">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                      <BookOpen className="w-8 h-8 text-white" />
                    </div>
                  </div>
                </div>

                {/* Overall Progress */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white font-semibold">التقدم الإجمالي</span>
                    <span className="text-white font-bold">{mockUser.overallProgress}% مكتمل</span>
                  </div>
                  <Progress value={animatedProgress} className="h-3 bg-white/20" />
                  <p className="text-blue-100 text-sm">
                    {mockUser.completedLectures.length} من {mockLectures.length} محاضرات مكتملة
                  </p>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full"></div>
            </div>

            {/* Smart CTA Button */}
            <Card className="border-0 shadow-lg bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20">
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <SmartCTAIcon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">{smartCTA.text}</h3>
                  <p className="text-slate-600 dark:text-slate-400">{smartCTA.subtext}</p>
                </div>
                <Button
                  onClick={smartCTA.action}
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  {smartCTA.text}
                  <ChevronLeft className="w-5 h-5 mr-2" />
                </Button>
              </CardContent>
            </Card>

            {/* Lectures List */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white">محاضرات الكورس</h3>
                <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                  {mockLectures.length} محاضرات
                </Badge>
              </div>

              <div className="grid gap-4">
                {mockLectures.map((lecture) => {
                  const state = getLectureState(lecture.id)

                  return (
                    <Card
                      key={lecture.id}
                      className={`transition-all duration-300 hover:shadow-lg ${
                        state === "locked"
                          ? "opacity-60 bg-slate-50 dark:bg-slate-800/50"
                          : state === "completed"
                            ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                            : "bg-white dark:bg-slate-800 hover:shadow-xl transform hover:-translate-y-1"
                      }`}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          {/* Lecture Number & Status Icon */}
                          <div className="flex-shrink-0">
                            <div
                              className={`w-16 h-16 rounded-xl flex items-center justify-center text-white font-bold text-lg ${
                                state === "locked"
                                  ? "bg-slate-400 dark:bg-slate-600"
                                  : state === "completed"
                                    ? "bg-green-500"
                                    : "bg-gradient-to-r from-blue-500 to-indigo-500"
                              }`}
                            >
                              {state === "locked" ? (
                                <Lock className="w-6 h-6" />
                              ) : state === "completed" ? (
                                <CheckCircle className="w-6 h-6" />
                              ) : (
                                <span>{lecture.number}</span>
                              )}
                            </div>
                          </div>

                          {/* Lecture Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex-1">
                                <h4
                                  className={`font-bold text-lg mb-1 ${
                                    state === "locked"
                                      ? "text-slate-500 dark:text-slate-400"
                                      : "text-slate-900 dark:text-white"
                                  }`}
                                >
                                  المحاضرة {lecture.number}: {lecture.title}
                                </h4>
                                <p
                                  className={`text-sm mb-2 ${
                                    state === "locked"
                                      ? "text-slate-400 dark:text-slate-500"
                                      : "text-slate-600 dark:text-slate-400"
                                  }`}
                                >
                                  {lecture.titleEn}
                                </p>
                                <p
                                  className={`text-sm mb-3 ${
                                    state === "locked"
                                      ? "text-slate-400 dark:text-slate-500"
                                      : "text-slate-600 dark:text-slate-400"
                                  }`}
                                >
                                  {lecture.description}
                                </p>
                                <div className="flex items-center gap-4">
                                  <Badge variant="outline" className="text-xs">
                                    <Clock className="w-3 h-3 ml-1" />
                                    {lecture.duration}
                                  </Badge>
                                  {state === "completed" && (
                                    <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs">
                                      <Star className="w-3 h-3 ml-1" />
                                      مكتملة
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              {/* Action Button */}
                              <div className="flex-shrink-0">
                                {state === "locked" ? (
                                  <Button disabled variant="ghost" className="text-slate-400">
                                    مقفلة
                                  </Button>
                                ) : state === "completed" ? (
                                  <Button
                                    variant="outline"
                                    className="text-green-600 border-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 bg-transparent"
                                  >
                                    أعد المشاهدة
                                  </Button>
                                ) : (
                                  <Button className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white">
                                    <Play className="w-4 h-4 ml-2" />
                                    ابدأ المحاضرة
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>

            {/* Final Exam Card */}
            <Card
              className={`border-2 ${mockUser.overallProgress === 100 ? "border-amber-300 bg-amber-50 dark:bg-amber-900/20" : "border-slate-200 dark:border-slate-700"}`}
            >
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      mockUser.overallProgress === 100
                        ? "bg-gradient-to-r from-amber-500 to-orange-500"
                        : "bg-slate-300 dark:bg-slate-600"
                    }`}
                  >
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">الاختبار النهائي</h3>
                  <p className="text-slate-600 dark:text-slate-400">Final Exam</p>
                </div>

                {mockUser.overallProgress === 100 ? (
                  <div>
                    <p className="text-slate-600 dark:text-slate-400 mb-6">
                      تهانينا! لقد أكملت جميع المحاضرات. حان الوقت لإجراء الاختبار النهائي.
                    </p>
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-4 text-lg rounded-xl"
                    >
                      ابدأ الاختبار النهائي
                      <ChevronLeft className="w-5 h-5 mr-2" />
                    </Button>
                  </div>
                ) : (
                  <div>
                    <p className="text-slate-500 dark:text-slate-400 mb-6">
                      أكمل جميع المحاضرات أولاً لفتح الاختبار النهائي
                    </p>
                    <Button disabled size="lg" variant="outline">
                      الاختبار مقفل
                      <Lock className="w-5 h-5 mr-2" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 bg-black/50 z-30 md:hidden" onClick={() => setSidebarOpen(false)} />
      )}
    </div>
  )
}
