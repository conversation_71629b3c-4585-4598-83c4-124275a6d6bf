import type React from "react";
import type { Metada<PERSON> } from "next";
import { Cairo, Inter } from "next/font/google";
import "./globals.css";
import { AccessibilityProvider } from "@/components/accessibility-provider";

const cairo = Cairo({
  subsets: ["latin", "arabic"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-cairo",
  display: "swap",
  preload: true,
  fallback: ["system-ui", "-apple-system", "sans-serif"],
});

const inter = Inter({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-inter",
  display: "swap",
  preload: true,
  fallback: ["system-ui", "-apple-system", "sans-serif"],
});

export const metadata: Metadata = {
  title: "كورس الإعداد للزواج - أبرشية شبين القناطر",
  description:
    "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
  keywords: "زواج, إعداد للزواج, كورس, مسيحي, أبرشية, شبين القناطر",
  authors: [{ name: "مطرانية شبين القناطر" }],
  creator: "مطرانية شبين القناطر",
  publisher: "مطرانية شبين القناطر",
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1, viewport-fit=cover",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#fbbf24" },
    { media: "(prefers-color-scheme: dark)", color: "#1e293b" },
  ],
  manifest: "/manifest.json",
  icons: {
    icon: [
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
  },
  openGraph: {
    type: "website",
    locale: "ar_EG",
    url: "https://mashora.diocese-shibin.org",
    title: "كورس الإعداد للزواج - مشورة",
    description:
      "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
    siteName: "مشورة - مطرانية شبين القناطر",
  },
  twitter: {
    card: "summary_large_image",
    title: "كورس الإعداد للزواج - مشورة",
    description:
      "استعد لحياة زوجية سعيدة ومستقرة من خلال كورس شامل يغطي كل ما تحتاج معرفته",
  },
  generator: "v0.app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl" className="dark" suppressHydrationWarning>
      <head>
        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/diocese-logo.png"
          as="image"
          type="image/png"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* Critical CSS hint */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            .above-fold { contain: layout style; }
            .critical-path { will-change: transform, opacity; }
          `,
          }}
        />
      </head>
      <body
        className={`${cairo.variable} ${inter.variable} font-cairo antialiased optimize-rendering`}
        suppressHydrationWarning
      >
        <AccessibilityProvider>
          <div id="main-content" className="min-h-screen">
            {children}
          </div>
        </AccessibilityProvider>

        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
