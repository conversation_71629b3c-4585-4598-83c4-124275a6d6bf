"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  ArrowRight,
  Clock,
  CheckCircle,
  X,
  Trophy,
  RotateCcw,
  ChevronLeft,
  Star,
  Target,
  BookOpen,
  Award,
  Flag,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useParams } from "next/navigation"

// Mock quiz data
const mockQuizzes = {
  "1": {
    id: 1,
    lectureTitle: "مقدمة عن سر الزيجة",
    timeLimit: 10, // minutes
    passingScore: 70,
    questions: [
      {
        id: 1,
        question: "ما هو المعنى الروحي لسر الزيجة في المسيحية؟",
        options: [
          "عقد اجتماعي بين رجل وامرأة",
          "سر مقدس يجمع بين رجل وامرأة في وحدة روحية وجسدية",
          "احتفال عائلي تقليدي",
          "مجرد طقس كنسي",
        ],
        correctAnswer: 1,
        explanation: "الزواج في المسيحية هو سر مقدس يرمز إلى اتحاد المسيح بالكنيسة",
      },
      {
        id: 2,
        question: "أي من الآيات التالية تتحدث عن الزواج؟",
        options: [
          '"لذلك يترك الرجل أباه وأمه ويلتصق بامرأته"',
          '"أحبوا أعداءكم"',
          '"اطلبوا أولاً ملكوت الله"',
          '"كونوا قديسين"',
        ],
        correctAnswer: 0,
        explanation: "هذه الآية من سفر التكوين تؤسس لمفهوم الزواج المسيحي",
      },
      {
        id: 3,
        question: "ما هي أهمية الصلاة في الحياة الزوجية؟",
        options: ["غير مهمة", "مهمة أحياناً", "أساسية لبناء علاقة روحية قوية", "مجرد تقليد"],
        correctAnswer: 2,
        explanation: "الصلاة المشتركة تقوي الرابط الروحي بين الزوجين",
      },
    ],
  },
  "2": {
    id: 2,
    lectureTitle: "التواصل الفعال بين الزوجين",
    timeLimit: 15,
    passingScore: 70,
    questions: [
      {
        id: 1,
        question: "ما هي أهم مهارات التواصل الفعال؟",
        options: [
          "الصراخ والعتاب",
          "الاستماع الفعال والتعبير بوضوح",
          "تجنب الحديث في المشاكل",
          "إلقاء اللوم على الطرف الآخر",
        ],
        correctAnswer: 1,
        explanation: "التواصل الفعال يتطلب الاستماع الجيد والتعبير الواضح عن المشاعر والأفكار",
      },
    ],
  },
}

export default function QuizPage() {
  const params = useParams()
  const quizId = params.id as string
  const quiz = mockQuizzes[quizId as keyof typeof mockQuizzes]

  // State management
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([])
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const [quizStarted, setQuizStarted] = useState(false)
  const [quizCompleted, setQuizCompleted] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [score, setScore] = useState(0)
  const [showSubmissionAnimation, setShowSubmissionAnimation] = useState(false)
  const [correctAnswersCount, setCorrectAnswersCount] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set())
  const [confidenceLevels, setConfidenceLevels] = useState<number[]>([])
  const [showReviewMode, setShowReviewMode] = useState(false)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Use ref to track if timer has been initialized
  const timerInitialized = useRef(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize selected answers when quiz is loaded
  useEffect(() => {
    if (quiz && selectedAnswers.length === 0) {
      setSelectedAnswers(new Array(quiz.questions.length).fill(-1))
    }
  }, [quiz, selectedAnswers.length])

  // Timer countdown effect
  useEffect(() => {
    if (quizStarted && timeLeft !== null && timeLeft > 0 && !quizCompleted && !showResults && !isSubmitting) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            // Time's up - submit quiz
            handleSubmitQuiz()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [quizStarted, timeLeft, quizCompleted, showResults, isSubmitting])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Auto-save progress
  useEffect(() => {
    if (quizStarted && !quizCompleted && !showResults) {
      const interval = setInterval(autoSaveProgress, 30000) // Save every 30 seconds
      return () => clearInterval(interval)
    }
  }, [quizStarted, quizCompleted, showResults, selectedAnswers, flaggedQuestions, confidenceLevels])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const handleFlagQuestion = (questionIndex: number) => {
    const newFlagged = new Set(flaggedQuestions)
    if (newFlagged.has(questionIndex)) {
      newFlagged.delete(questionIndex)
    } else {
      newFlagged.add(questionIndex)
    }
    setFlaggedQuestions(newFlagged)
  }

  const handleConfidenceSelect = (level: number) => {
    const newConfidence = [...confidenceLevels]
    newConfidence[currentQuestion] = level
    setConfidenceLevels(newConfidence)
  }

  const autoSaveProgress = () => {
    if (autoSaveEnabled && quizStarted && !quizCompleted) {
      localStorage.setItem(
        `quiz_${quizId}_progress`,
        JSON.stringify({
          currentQuestion,
          selectedAnswers,
          flaggedQuestions: Array.from(flaggedQuestions),
          confidenceLevels,
          timeLeft,
          timestamp: new Date().toISOString(),
        }),
      )
      setLastSaved(new Date())
    }
  }

  const loadSavedProgress = () => {
    const saved = localStorage.getItem(`quiz_${quizId}_progress`)
    if (saved) {
      try {
        const data = JSON.parse(saved)
        const savedTime = new Date(data.timestamp)
        const now = new Date()
        const timeDiff = (now.getTime() - savedTime.getTime()) / 1000

        if (timeDiff < 3600) {
          // Only restore if less than 1 hour old
          setCurrentQuestion(data.currentQuestion || 0)
          setSelectedAnswers(data.selectedAnswers || new Array(quiz?.questions.length || 0).fill(-1))
          setFlaggedQuestions(new Set(data.flaggedQuestions || []))
          setConfidenceLevels(data.confidenceLevels || [])
          if (data.timeLeft && data.timeLeft > 0) {
            setTimeLeft(Math.max(0, data.timeLeft - timeDiff))
          }
          return true
        }
      } catch (error) {
        console.error("Failed to load saved progress:", error)
      }
    }
    return false
  }

  const clearSavedProgress = () => {
    localStorage.removeItem(`quiz_${quizId}_progress`)
  }

  const handleStartQuiz = () => {
    if (!quiz) return

    // Check for saved progress
    const hasSavedProgress = loadSavedProgress()

    if (!hasSavedProgress) {
      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Reset all states
      setQuizStarted(false)
      setQuizCompleted(false)
      setShowResults(false)
      setShowSubmissionAnimation(false)
      setCurrentQuestion(0)
      setSelectedAnswers(new Array(quiz.questions.length).fill(-1))
      setFlaggedQuestions(new Set())
      setConfidenceLevels(new Array(quiz.questions.length).fill(0))
      setScore(0)
      setCorrectAnswersCount(0)
      setIsSubmitting(false)
      setTimeLeft(null)
      timerInitialized.current = false

      // Start quiz after a brief delay to ensure clean state
      setTimeout(() => {
        setTimeLeft(quiz.timeLimit * 60) // Convert minutes to seconds
        setQuizStarted(true)
        timerInitialized.current = true
      }, 100)
    } else {
      setQuizStarted(true)
      timerInitialized.current = true
    }
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (quizCompleted || showResults || isSubmitting || !quizStarted) return

    const newAnswers = [...selectedAnswers]
    newAnswers[currentQuestion] = answerIndex
    setSelectedAnswers(newAnswers)
  }

  const handleNextQuestion = () => {
    if (quizCompleted || showResults || isSubmitting || !quizStarted) return

    if (currentQuestion < (quiz?.questions.length || 0) - 1) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const handlePreviousQuestion = () => {
    if (quizCompleted || showResults || isSubmitting || !quizStarted) return

    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleSubmitQuiz = () => {
    if (!quiz || quizCompleted || showResults || isSubmitting) return

    // Clear the timer
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Clear saved progress
    clearSavedProgress()

    // Prevent multiple submissions
    setIsSubmitting(true)
    setShowSubmissionAnimation(true)

    // Simulate processing time for better UX
    setTimeout(() => {
      let correctAnswers = 0
      quiz.questions.forEach((question, index) => {
        if (selectedAnswers[index] === question.correctAnswer) {
          correctAnswers++
        }
      })

      const finalScore = Math.round((correctAnswers / quiz.questions.length) * 100)

      // Update states in correct order
      setScore(finalScore)
      setCorrectAnswersCount(correctAnswers)
      setQuizCompleted(true)
      setShowSubmissionAnimation(false)
      setShowResults(true)
      setIsSubmitting(false)
    }, 2000)
  }

  const handleRetakeQuiz = () => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Reset all states for retake
    setCurrentQuestion(0)
    setSelectedAnswers(new Array(quiz?.questions.length || 0).fill(-1))
    setQuizStarted(false)
    setQuizCompleted(false)
    setShowResults(false)
    setScore(0)
    setCorrectAnswersCount(0)
    setShowSubmissionAnimation(false)
    setIsSubmitting(false)
    setTimeLeft(null)
    timerInitialized.current = false
  }

  const getPerformanceMessage = (score: number) => {
    if (score >= 90) return { message: "أداء ممتاز! استمر في التميز", color: "text-emerald-600", icon: Star }
    if (score >= 80) return { message: "أداء جيد جداً! تقدم رائع", color: "text-green-600", icon: Trophy }
    if (score >= 70) return { message: "أداء جيد! لقد نجحت", color: "text-blue-600", icon: CheckCircle }
    return { message: "يحتاج إلى تحسين، حاول مرة أخرى", color: "text-amber-600", icon: Target }
  }

  // Check if all questions are answered
  const allQuestionsAnswered = selectedAnswers.every((answer) => answer !== -1)
  const canSubmit = quizStarted && !quizCompleted && !showResults && !isSubmitting && timeLeft !== null

  if (!quiz) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950">
        <div className="text-center">
          <div className="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <X className="w-10 h-10 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">الاختبار غير موجود</h1>
          <p className="text-slate-600 dark:text-slate-400 mb-6">عذراً، لم نتمكن من العثور على هذا الاختبار</p>
          <Link href="/dashboard">
            <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl">
              العودة للوحة التحكم
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  // Submission Animation Screen
  if (showSubmissionAnimation) {
    return (
      <div
        className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo flex items-center justify-center"
        dir="rtl"
      >
        <Card className="w-full max-w-md mx-4 text-center">
          <CardContent className="p-8">
            <div className="relative w-24 h-24 mx-auto mb-6">
              <div className="absolute inset-0 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-pulse"></div>
              <div className="absolute inset-2 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <div className="absolute inset-4 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <BookOpen className="w-8 h-8 text-blue-600 animate-bounce" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">جاري تقييم إجاباتك...</h2>
            <p className="text-slate-600 dark:text-slate-400 mb-6">
              يرجى الانتظار بينما نقوم بمراجعة إجاباتك وحساب النتيجة
            </p>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full animate-pulse"
                style={{ width: "70%" }}
              ></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <ArrowRight className="w-5 h-5" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative w-10 h-10 rounded-xl overflow-hidden">
                <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900 dark:text-white">مشورة</h1>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {!quizStarted ? (
            /* Quiz Introduction */
            <Card className="text-center overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20"></div>
              <CardHeader className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <BookOpen className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
                  اختبار المحاضرة
                </CardTitle>
                <h2 className="text-xl text-slate-600 dark:text-slate-400">{quiz.lectureTitle}</h2>
              </CardHeader>
              <CardContent className="space-y-8 relative">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                    <Clock className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                    <h3 className="font-semibold text-slate-900 dark:text-white">المدة المحددة</h3>
                    <p className="text-slate-600 dark:text-slate-400">{quiz.timeLimit} دقيقة</p>
                  </div>
                  <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                    <Target className="w-8 h-8 text-green-600 mx-auto mb-3" />
                    <h3 className="font-semibold text-slate-900 dark:text-white">درجة النجاح</h3>
                    <p className="text-slate-600 dark:text-slate-400">{quiz.passingScore}%</p>
                  </div>
                  <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                    <Trophy className="w-8 h-8 text-amber-600 mx-auto mb-3" />
                    <h3 className="font-semibold text-slate-900 dark:text-white">عدد الأسئلة</h3>
                    <p className="text-slate-600 dark:text-slate-400">{quiz.questions.length} أسئلة</p>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                    تعليمات الاختبار
                  </h3>
                  <ul className="text-slate-600 dark:text-slate-400 space-y-3 text-right">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      اقرأ كل سؤال بعناية قبل الإجابة
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      يمكنك العودة لتعديل إجاباتك قبل التسليم
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      سيتم تسليم الاختبار تلقائياً عند انتهاء الوقت
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      يجب الحصول على {quiz.passingScore}% على الأقل للنجاح
                    </li>
                  </ul>
                </div>

                <Button
                  onClick={handleStartQuiz}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-12 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  ابدأ الاختبار
                  <ChevronLeft className="w-5 h-5 mr-3" />
                </Button>
              </CardContent>
            </Card>
          ) : showResults ? (
            /* Enhanced Quiz Results */
            <div className="space-y-6">
              {/* Main Result Card */}
              <Card className="text-center overflow-hidden relative">
                <div
                  className={`absolute inset-0 ${
                    score >= quiz.passingScore
                      ? "bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20"
                      : "bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-950/20 dark:to-orange-950/20"
                  }`}
                ></div>
                <CardHeader className="relative">
                  <div
                    className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg ${
                      score >= quiz.passingScore
                        ? "bg-gradient-to-r from-green-500 to-emerald-500"
                        : "bg-gradient-to-r from-amber-500 to-orange-500"
                    }`}
                  >
                    {(() => {
                      const { icon: Icon } = getPerformanceMessage(score)
                      return <Icon className="w-12 h-12 text-white" />
                    })()}
                  </div>
                  <CardTitle className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
                    {score >= quiz.passingScore ? "تهانينا!" : "حاول مرة أخرى"}
                  </CardTitle>
                  <p className="text-xl text-slate-600 dark:text-slate-400 mb-2">
                    {getPerformanceMessage(score).message}
                  </p>
                  <div className={`text-5xl font-bold mb-4 ${getPerformanceMessage(score).color}`}>{score}%</div>
                </CardHeader>
                <CardContent className="space-y-8 relative">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                      <div className="text-3xl font-bold text-slate-900 dark:text-white mb-2">{score}%</div>
                      <p className="text-slate-600 dark:text-slate-400">النتيجة النهائية</p>
                    </div>
                    <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                      <div className="text-3xl font-bold text-green-600 mb-2">{correctAnswersCount}</div>
                      <p className="text-slate-600 dark:text-slate-400">إجابات صحيحة</p>
                    </div>
                    <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                      <div className="text-3xl font-bold text-red-600 mb-2">
                        {quiz.questions.length - correctAnswersCount}
                      </div>
                      <p className="text-slate-600 dark:text-slate-400">إجابات خاطئة</p>
                    </div>
                    <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm">
                      <div className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                        {quiz.questions.length}
                      </div>
                      <p className="text-slate-600 dark:text-slate-400">إجمالي الأسئلة</p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400">
                      <span>التقدم</span>
                      <span>{score}%</span>
                    </div>
                    <Progress value={score} className="h-3" />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    {score >= quiz.passingScore ? (
                      <>
                        <Link href="/dashboard">
                          <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto">
                            <CheckCircle className="w-5 h-5 ml-2" />
                            العودة للوحة التحكم
                          </Button>
                        </Link>
                        <Button
                          onClick={handleRetakeQuiz}
                          variant="outline"
                          className="border-green-300 text-green-700 hover:bg-green-50 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/20 px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto bg-transparent"
                        >
                          <RotateCcw className="w-5 h-5 ml-2" />
                          تحسين النتيجة
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          onClick={handleRetakeQuiz}
                          className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          <RotateCcw className="w-5 h-5 ml-2" />
                          إعادة المحاولة
                        </Button>
                        <Link href="/dashboard">
                          <Button
                            variant="outline"
                            className="border-slate-300 text-slate-700 hover:bg-slate-50 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800 px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto bg-transparent"
                          >
                            العودة للوحة التحكم
                          </Button>
                        </Link>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Detailed Results */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-slate-900 dark:text-white text-right flex items-center gap-3">
                    <Award className="w-6 h-6 text-blue-600" />
                    تفاصيل الإجابات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {quiz.questions.map((question, index) => {
                    const isCorrect = selectedAnswers[index] === question.correctAnswer
                    return (
                      <Card
                        key={question.id}
                        className={`text-right transition-all duration-300 hover:shadow-md ${
                          isCorrect
                            ? "border-green-200 dark:border-green-800 bg-green-50/30 dark:bg-green-900/10"
                            : "border-red-200 dark:border-red-800 bg-red-50/30 dark:bg-red-900/10"
                        }`}
                      >
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                                isCorrect ? "bg-green-100 dark:bg-green-900/30" : "bg-red-100 dark:bg-red-900/30"
                              }`}
                            >
                              {isCorrect ? (
                                <CheckCircle className="w-5 h-5 text-green-600" />
                              ) : (
                                <X className="w-5 h-5 text-red-600" />
                              )}
                            </div>
                            <div className="flex-1 space-y-3">
                              <h4 className="font-semibold text-slate-900 dark:text-white text-lg">
                                السؤال {index + 1}: {question.question}
                              </h4>
                              <div className="space-y-2">
                                <p className="text-sm text-slate-600 dark:text-slate-400">
                                  <span className="font-medium">إجابتك:</span>{" "}
                                  {question.options[selectedAnswers[index]] || "لم تجب"}
                                </p>
                                {!isCorrect && (
                                  <p className="text-sm text-green-600 dark:text-green-400">
                                    <span className="font-medium">الإجابة الصحيحة:</span>{" "}
                                    {question.options[question.correctAnswer]}
                                  </p>
                                )}
                                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                                  <p className="text-sm text-blue-800 dark:text-blue-200">
                                    <span className="font-medium">التفسير:</span> {question.explanation}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </CardContent>
              </Card>
            </div>
          ) : (
            /* Quiz Questions */
            <div className="space-y-6">
              {/* Quiz Header */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2">
                        السؤال {currentQuestion + 1} من {quiz.questions.length}
                      </Badge>
                      {timeLeft !== null && (
                        <Badge
                          variant="outline"
                          className={`flex items-center gap-2 ${
                            timeLeft < 300
                              ? "border-red-300 text-red-600 animate-pulse"
                              : timeLeft < 600
                                ? "border-amber-300 text-amber-600"
                                : ""
                          }`}
                        >
                          <Clock className="w-3 h-3" />
                          {formatTime(timeLeft)}
                          {timeLeft < 300 && <span className="text-xs">(تحذير!)</span>}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Progress value={((currentQuestion + 1) / quiz.questions.length) * 100} className="h-2" />
                </CardContent>
              </Card>

              {/* Current Question */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-slate-900 dark:text-white text-right">
                    {quiz.questions[currentQuestion].question}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {quiz.questions[currentQuestion].options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleAnswerSelect(index)}
                      disabled={!canSubmit}
                      className={`w-full p-4 text-right rounded-xl border-2 transition-all duration-200 hover:scale-[1.02] disabled:cursor-not-allowed disabled:opacity-50 ${
                        selectedAnswers[currentQuestion] === index
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 shadow-md"
                          : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white hover:shadow-sm"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                            selectedAnswers[currentQuestion] === index
                              ? "border-blue-500 bg-blue-500 scale-110"
                              : "border-slate-300 dark:border-slate-600"
                          }`}
                        >
                          {selectedAnswers[currentQuestion] === index && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                        <span className="flex-1">{option}</span>
                      </div>
                    </button>
                  ))}

                  {/* Confidence Level & Flag Options */}
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-6 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                    <div className="flex flex-col gap-2">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        مستوى الثقة في الإجابة:
                      </span>
                      <div className="flex gap-2">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <button
                            key={level}
                            onClick={() => handleConfidenceSelect(level)}
                            disabled={!canSubmit}
                            className={`w-8 h-8 rounded-full border-2 text-sm font-bold transition-all duration-200 ${
                              confidenceLevels[currentQuestion] === level
                                ? "border-blue-500 bg-blue-500 text-white"
                                : "border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:border-blue-400"
                            }`}
                          >
                            {level}
                          </button>
                        ))}
                      </div>
                      <span className="text-xs text-slate-500 dark:text-slate-400">1 = غير متأكد، 5 = متأكد جداً</span>
                    </div>

                    <div className="flex items-center gap-3">
                      <Button
                        onClick={() => handleFlagQuestion(currentQuestion)}
                        disabled={!canSubmit}
                        variant="outline"
                        size="sm"
                        className={`${
                          flaggedQuestions.has(currentQuestion)
                            ? "border-amber-500 bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300"
                            : "border-slate-300 text-slate-600 dark:border-slate-600 dark:text-slate-400"
                        }`}
                      >
                        <Flag className="w-4 h-4 ml-2" />
                        {flaggedQuestions.has(currentQuestion) ? "إلغاء العلامة" : "وضع علامة"}
                      </Button>

                      {lastSaved && (
                        <div className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1">
                          <CheckCircle className="w-3 h-3" />
                          تم الحفظ {new Date(lastSaved).toLocaleTimeString("ar-EG")}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between items-center">
                <Button
                  onClick={handlePreviousQuestion}
                  disabled={currentQuestion === 0 || !canSubmit}
                  variant="outline"
                  className="px-6 py-3 bg-transparent transition-all duration-300 hover:scale-105 disabled:hover:scale-100"
                >
                  السؤال السابق
                </Button>

                <div className="flex gap-3">
                  {currentQuestion === quiz.questions.length - 1 ? (
                    <Button
                      onClick={handleSubmitQuiz}
                      disabled={!allQuestionsAnswered || !canSubmit}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:hover:scale-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {!allQuestionsAnswered ? "أجب على جميع الأسئلة" : "تسليم الاختبار"}
                    </Button>
                  ) : (
                    <Button
                      onClick={handleNextQuestion}
                      disabled={selectedAnswers[currentQuestion] === -1 || !canSubmit}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 disabled:hover:scale-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      السؤال التالي
                      <ChevronLeft className="w-4 h-4 mr-2" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Enhanced Question Navigation */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">التنقل السريع</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {flaggedQuestions.size} مُعلَّم
                      </Badge>
                      <Button
                        onClick={() => setShowReviewMode(!showReviewMode)}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                      >
                        {showReviewMode ? "إخفاء المراجعة" : "وضع المراجعة"}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-5 gap-2">
                    {quiz.questions.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => canSubmit && setCurrentQuestion(index)}
                        disabled={!canSubmit}
                        className={`relative w-12 h-12 rounded-lg border-2 font-semibold transition-all duration-200 hover:scale-110 disabled:cursor-not-allowed disabled:opacity-50 ${
                          index === currentQuestion
                            ? "border-blue-500 bg-blue-500 text-white shadow-lg"
                            : selectedAnswers[index] !== -1
                              ? "border-green-300 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:shadow-md"
                              : "border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:border-slate-400 hover:shadow-sm"
                        }`}
                      >
                        {index + 1}
                        {flaggedQuestions.has(index) && (
                          <Flag className="absolute -top-1 -right-1 w-3 h-3 text-amber-500 fill-current" />
                        )}
                        {confidenceLevels[index] > 0 && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                            {confidenceLevels[index]}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>

                  {/* Review Summary */}
                  {showReviewMode && (
                    <div className="mt-6 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-green-800 dark:text-green-200">
                            تم الإجابة: {selectedAnswers.filter((a) => a !== -1).length}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                          <Flag className="w-4 h-4 text-amber-600" />
                          <span className="text-amber-800 dark:text-amber-200">مُعلَّم: {flaggedQuestions.size}</span>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <Target className="w-4 h-4 text-blue-600" />
                          <span className="text-blue-800 dark:text-blue-200">
                            متوسط الثقة:{" "}
                            {confidenceLevels.length > 0
                              ? (
                                  confidenceLevels.reduce((a, b) => a + b, 0) /
                                    confidenceLevels.filter((c) => c > 0).length || 0
                                ).toFixed(1)
                              : 0}
                          </span>
                        </div>
                      </div>

                      {flaggedQuestions.size > 0 && (
                        <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
                          <h4 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">
                            الأسئلة المُعلَّمة للمراجعة:
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {Array.from(flaggedQuestions).map((questionIndex) => (
                              <button
                                key={questionIndex}
                                onClick={() => canSubmit && setCurrentQuestion(questionIndex)}
                                disabled={!canSubmit}
                                className="px-3 py-1 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 rounded-full text-sm hover:bg-amber-300 dark:hover:bg-amber-700 transition-colors"
                              >
                                السؤال {questionIndex + 1}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex items-center gap-4 mt-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-blue-500 rounded"></div>
                      <span className="text-slate-600 dark:text-slate-400">السؤال الحالي</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-50 dark:bg-green-900/20 border border-green-300 rounded"></div>
                      <span className="text-slate-600 dark:text-slate-400">تم الإجابة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border border-slate-300 dark:border-slate-600 rounded"></div>
                      <span className="text-slate-600 dark:text-slate-400">لم يتم الإجابة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Flag className="w-4 h-4 text-amber-500" />
                      <span className="text-slate-600 dark:text-slate-400">مُعلَّم</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
