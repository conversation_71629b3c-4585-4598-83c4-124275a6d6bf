"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  ArrowRight,
  Clock,
  CheckCircle,
  X,
  AlertCircle,
  Trophy,
  RotateCcw,
  ChevronLeft,
  Award,
  FileText,
  Star,
  Target,
  BookOpen,
  Crown,
  Medal,
  Zap,
  Menu,
  Flag,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock final exam data
const mockFinalExam = {
  id: "final",
  title: "الاختبار النهائي - كورس الإعداد للزواج",
  timeLimit: 60, // minutes
  passingScore: 80,
  questions: [
    {
      id: 1,
      question: "ما هو المعنى الروحي لسر الزيجة في المسيحية؟",
      options: [
        "عقد اجتماعي بين رجل وامرأة",
        "سر مقدس يجمع بين رجل وامرأة في وحدة روحية وجسدية",
        "احتفال عائلي تقليدي",
        "مجرد طقس كنسي",
      ],
      correctAnswer: 1,
      explanation: "الزواج في المسيحية هو سر مقدس يرمز إلى اتحاد المسيح بالكنيسة",
    },
    {
      id: 2,
      question: "ما هي أهم مهارات التواصل الفعال بين الزوجين؟",
      options: [
        "الصراخ والعتاب",
        "الاستماع الفعال والتعبير بوضوح",
        "تجنب الحديث في المشاكل",
        "إلقاء اللوم على الطرف الآخر",
      ],
      correctAnswer: 1,
      explanation: "التواصل الفعال يتطلب الاستماع الجيد والتعبير الواضح عن المشاعر والأفكار",
    },
    {
      id: 3,
      question: "كيف يجب إدارة الأموال في الحياة الزوجية؟",
      options: [
        "كل طرف يدير أمواله بشكل منفصل",
        "التخطيط المشترك والشفافية المالية",
        "إخفاء المصاريف عن الطرف الآخر",
        "إنفاق الأموال دون تخطيط",
      ],
      correctAnswer: 1,
      explanation: "الإدارة المالية الناجحة تتطلب التخطيط المشترك والشفافية بين الزوجين",
    },
    {
      id: 4,
      question: "ما هو دور الاحترام المتبادل في العلاقة الزوجية؟",
      options: [
        "غير مهم في العلاقة",
        "أساس قوي لبناء علاقة صحية ومستقرة",
        "مطلوب من طرف واحد فقط",
        "يأتي تلقائياً مع الوقت",
      ],
      correctAnswer: 1,
      explanation: "الاحترام المتبادل هو حجر الأساس في أي علاقة زوجية ناجحة",
    },
    {
      id: 5,
      question: "كيف يجب التعامل مع الأهل والعائلة الممتدة؟",
      options: [
        "قطع العلاقة معهم تماماً",
        "إقامة حدود صحية مع الحفاظ على الاحترام",
        "السماح لهم بالتدخل في كل شيء",
        "تجاهلهم تماماً",
      ],
      correctAnswer: 1,
      explanation: "الحدود الصحية مع العائلة تحافظ على استقلالية الزوجين مع الحفاظ على العلاقات الطيبة",
    },
    {
      id: 6,
      question: "ما أهمية التخطيط للمستقبل في الحياة الزوجية؟",
      options: [
        "غير ضروري، الحياة تسير بشكل طبيعي",
        "مهم لتحديد الأهداف المشتركة وبناء مستقبل مستقر",
        "يسبب التوتر والقلق",
        "يقيد الحرية الشخصية",
      ],
      correctAnswer: 1,
      explanation: "التخطيط للمستقبل يساعد الزوجين على تحقيق أهدافهما المشتركة وبناء حياة مستقرة",
    },
    {
      id: 7,
      question: "ما دور الصلاة في الحياة الزوجية المسيحية؟",
      options: ["مجرد تقليد قديم", "تقوي الرابط الروحي وتوحد القلوب", "غير مهمة في العلاقة", "تسبب الخلافات الدينية"],
      correctAnswer: 1,
      explanation: "الصلاة المشتركة تقوي الرابط الروحي بين الزوجين وتجعل الله محور حياتهما",
    },
    {
      id: 8,
      question: "كيف يمكن حل النزاعات الزوجية بطريقة صحية؟",
      options: [
        "تجنب الحديث في المشكلة",
        "الحوار الهادئ والبحث عن حلول مشتركة",
        "إشراك الأهل في كل خلاف",
        "الصمت والتجاهل",
      ],
      correctAnswer: 1,
      explanation: "حل النزاعات يتطلب حواراً هادئاً ومحترماً للوصول إلى حلول ترضي الطرفين",
    },
    {
      id: 9,
      question: "ما أهمية التربية المسيحية للأطفال؟",
      options: [
        "غير مهمة في العصر الحديث",
        "أساسية لبناء شخصية متوازنة وقيم صحيحة",
        "تقيد حرية الطفل",
        "مسؤولية المدرسة فقط",
      ],
      correctAnswer: 1,
      explanation: "التربية المسيحية تزرع القيم الصحيحة وتبني شخصية متوازنة للأطفال",
    },
    {
      id: 10,
      question: "ما هي علامات الزواج الناجح؟",
      options: [
        "عدم وجود خلافات نهائياً",
        "التواصل الجيد والاحترام المتبادل والنمو المشترك",
        "الاتفاق في كل شيء",
        "عدم الحاجة للحديث مع الطرف الآخر",
      ],
      correctAnswer: 1,
      explanation: "الزواج الناجح يتميز بالتواصل الصحي والاحترام والنمو المستمر للعلاقة",
    },
  ],
}

export default function FinalExamPage() {
  // State management with proper initialization
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([])
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const [examStarted, setExamStarted] = useState(false)
  const [examCompleted, setExamCompleted] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [score, setScore] = useState(0)
  const [showWarning, setShowWarning] = useState(false)
  const [showSubmissionAnimation, setShowSubmissionAnimation] = useState(false)
  const [correctAnswersCount, setCorrectAnswersCount] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showQuickNav, setShowQuickNav] = useState(false)
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set())
  const [confidenceLevels, setConfidenceLevels] = useState<number[]>([])
  const [showReviewMode, setShowReviewMode] = useState(false)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [examAttempts, setExamAttempts] = useState(0)
  const [showFinalReview, setShowFinalReview] = useState(false)

  // Refs for proper timer management
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const timerInitialized = useRef(false)

  // Initialize timer only when exam starts
  useEffect(() => {
    if (examStarted && !examCompleted && !showResults && !timerInitialized.current) {
      setTimeLeft(mockFinalExam.timeLimit * 60) // Convert to seconds
      timerInitialized.current = true
    }
  }, [examStarted, examCompleted, showResults])

  // Timer countdown with proper cleanup
  useEffect(() => {
    if (timeLeft !== null && timeLeft > 0 && examStarted && !examCompleted && !showResults && !isSubmitting) {
      timerRef.current = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
    } else if (
      timeLeft === 0 &&
      examStarted &&
      !examCompleted &&
      !showResults &&
      !isSubmitting &&
      timerInitialized.current
    ) {
      // Only auto-submit if timer was actually initialized and counted down
      handleSubmitExam()
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [timeLeft, examStarted, examCompleted, showResults, isSubmitting])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
    }
  }, [])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const handleStartExam = () => {
    // Check for saved progress
    const hasSavedProgress = loadSavedProgress()

    if (!hasSavedProgress) {
      // Reset all states to ensure clean start
      setExamStarted(true)
      setExamCompleted(false)
      setShowResults(false)
      setShowSubmissionAnimation(false)
      setShowWarning(false)
      setCurrentQuestion(0)
      setSelectedAnswers(new Array(mockFinalExam.questions.length).fill(-1))
      setFlaggedQuestions(new Set())
      setConfidenceLevels(new Array(mockFinalExam.questions.length).fill(0))
      setScore(0)
      setCorrectAnswersCount(0)
      setIsSubmitting(false)
      setTimeLeft(null)
      timerInitialized.current = false

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }
    } else {
      setExamStarted(true)
      timerInitialized.current = true
    }
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (examCompleted || showResults || isSubmitting) return

    const newAnswers = [...selectedAnswers]
    newAnswers[currentQuestion] = answerIndex
    setSelectedAnswers(newAnswers)
  }

  const handleNextQuestion = () => {
    if (examCompleted || showResults || isSubmitting) return

    if (currentQuestion < mockFinalExam.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const handlePreviousQuestion = () => {
    if (examCompleted || showResults || isSubmitting) return

    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleSubmitExam = () => {
    if (examCompleted || showResults || isSubmitting) return

    // Clear timer
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }

    // Increment exam attempts
    const newAttempts = examAttempts + 1
    setExamAttempts(newAttempts)

    // Clear saved progress
    clearSavedProgress()

    // Prevent multiple submissions
    setIsSubmitting(true)
    setShowSubmissionAnimation(true)
    setShowWarning(false)

    // Simulate processing time for better UX
    setTimeout(() => {
      let correctAnswers = 0
      mockFinalExam.questions.forEach((question, index) => {
        if (selectedAnswers[index] === question.correctAnswer) {
          correctAnswers++
        }
      })

      const finalScore = Math.round((correctAnswers / mockFinalExam.questions.length) * 100)

      // Update states in correct order
      setScore(finalScore)
      setCorrectAnswersCount(correctAnswers)
      setExamCompleted(true)
      setShowSubmissionAnimation(false)
      setShowResults(true)
      setIsSubmitting(false)
    }, 3000)
  }

  const handleRetakeExam = () => {
    // Clear timer first
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }

    // Reset all states for retake
    setTimeout(() => {
      setCurrentQuestion(0)
      setSelectedAnswers(new Array(mockFinalExam.questions.length).fill(-1))
      setExamStarted(false)
      setExamCompleted(false)
      setShowResults(false)
      setScore(0)
      setCorrectAnswersCount(0)
      setShowWarning(false)
      setShowSubmissionAnimation(false)
      setIsSubmitting(false)
      setTimeLeft(null)
      timerInitialized.current = false
    }, 100)
  }

  const getPerformanceLevel = (score: number) => {
    if (score >= 95)
      return {
        level: "ممتاز جداً",
        message: "أداء استثنائي! أنت مستعد تماماً للزواج",
        color: "text-purple-600",
        bgColor: "from-purple-500 to-pink-500",
        icon: Crown,
      }
    if (score >= 90)
      return {
        level: "ممتاز",
        message: "أداء رائع! لديك فهم عميق للحياة الزوجية",
        color: "text-emerald-600",
        bgColor: "from-emerald-500 to-teal-500",
        icon: Star,
      }
    if (score >= 85)
      return {
        level: "جيد جداً",
        message: "أداء متميز! تقدم ملحوظ في الفهم",
        color: "text-green-600",
        bgColor: "from-green-500 to-emerald-500",
        icon: Medal,
      }
    if (score >= 80)
      return {
        level: "جيد",
        message: "أداء جيد! لقد نجحت في الاختبار",
        color: "text-blue-600",
        bgColor: "from-blue-500 to-indigo-500",
        icon: Trophy,
      }
    return {
      level: "يحتاج تحسين",
      message: "لا تقلق، يمكنك المحاولة مرة أخرى",
      color: "text-amber-600",
      bgColor: "from-amber-500 to-orange-500",
      icon: Target,
    }
  }

  // Check validation states
  const unansweredQuestions = selectedAnswers.filter((answer) => answer === -1).length
  const allQuestionsAnswered = selectedAnswers.every((answer) => answer !== -1)
  const canSubmit = examStarted && !examCompleted && !showResults && !isSubmitting

  const handleFlagQuestion = (questionIndex: number) => {
    const newFlagged = new Set(flaggedQuestions)
    if (newFlagged.has(questionIndex)) {
      newFlagged.delete(questionIndex)
    } else {
      newFlagged.add(questionIndex)
    }
    setFlaggedQuestions(newFlagged)
  }

  const handleConfidenceSelect = (level: number) => {
    const newConfidence = [...confidenceLevels]
    newConfidence[currentQuestion] = level
    setConfidenceLevels(newConfidence)
  }

  const autoSaveProgress = () => {
    if (autoSaveEnabled && examStarted && !examCompleted) {
      localStorage.setItem(
        "final_exam_progress",
        JSON.stringify({
          currentQuestion,
          selectedAnswers,
          flaggedQuestions: Array.from(flaggedQuestions),
          confidenceLevels,
          timeLeft,
          examAttempts,
          timestamp: new Date().toISOString(),
        }),
      )
      setLastSaved(new Date())
    }
  }

  const loadSavedProgress = () => {
    const saved = localStorage.getItem("final_exam_progress")
    if (saved) {
      try {
        const data = JSON.parse(saved)
        const savedTime = new Date(data.timestamp)
        const now = new Date()
        const timeDiff = (now.getTime() - savedTime.getTime()) / 1000

        if (timeDiff < 7200) {
          // Only restore if less than 2 hours old for final exam
          setCurrentQuestion(data.currentQuestion || 0)
          setSelectedAnswers(data.selectedAnswers || new Array(mockFinalExam.questions.length).fill(-1))
          setFlaggedQuestions(new Set(data.flaggedQuestions || []))
          setConfidenceLevels(data.confidenceLevels || [])
          setExamAttempts(data.examAttempts || 0)
          if (data.timeLeft && data.timeLeft > 0) {
            setTimeLeft(Math.max(0, data.timeLeft - timeDiff))
          }
          return true
        }
      } catch (error) {
        console.error("Failed to load saved progress:", error)
      }
    }
    return false
  }

  const clearSavedProgress = () => {
    localStorage.removeItem("final_exam_progress")
  }

  const getQuestionDifficulty = (questionIndex: number) => {
    // Mock difficulty based on question content complexity
    const difficulties = ["سهل", "متوسط", "صعب"]
    return difficulties[questionIndex % 3]
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "سهل":
        return "text-green-600 bg-green-100 dark:bg-green-900/20"
      case "متوسط":
        return "text-amber-600 bg-amber-100 dark:bg-amber-900/20"
      case "صعب":
        return "text-red-600 bg-red-100 dark:bg-red-900/20"
      default:
        return "text-slate-600 bg-slate-100 dark:bg-slate-900/20"
    }
  }

  // Auto-save progress
  useEffect(() => {
    if (examStarted && !examCompleted && !showResults) {
      const interval = setInterval(autoSaveProgress, 30000) // Save every 30 seconds
      return () => clearInterval(interval)
    }
  }, [examStarted, examCompleted, showResults, selectedAnswers, flaggedQuestions, confidenceLevels])

  // Submission Animation Screen
  if (showSubmissionAnimation) {
    return (
      <div
        className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo flex items-center justify-center p-4"
        dir="rtl"
      >
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-8 sm:p-12">
            <div className="relative w-24 h-24 sm:w-32 sm:h-32 mx-auto mb-6 sm:mb-8">
              <div className="absolute inset-0 border-4 border-amber-200 dark:border-amber-800 rounded-full animate-pulse"></div>
              <div className="absolute inset-2 border-4 border-amber-500 border-t-transparent rounded-full animate-spin"></div>
              <div
                className="absolute inset-4 border-4 border-amber-300 border-t-transparent rounded-full animate-spin"
                style={{ animationDirection: "reverse" }}
              ></div>
              <div className="absolute inset-6 sm:inset-8 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                <Award className="w-8 h-8 sm:w-12 sm:h-12 text-amber-600 animate-bounce" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900 dark:text-white mb-4 sm:mb-6">
              جاري تقييم الاختبار النهائي...
            </h2>
            <p className="text-sm sm:text-base lg:text-lg text-slate-600 dark:text-slate-400 mb-6 sm:mb-8">
              يرجى الانتظار بينما نقوم بمراجعة إجاباتك وحساب النتيجة النهائية
            </p>
            <div className="space-y-4">
              <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-amber-500 to-orange-500 h-3 rounded-full animate-pulse"
                  style={{ width: "85%" }}
                ></div>
              </div>
              <div className="flex justify-center space-x-2">
                <div className="w-3 h-3 bg-amber-500 rounded-full animate-bounce"></div>
                <div
                  className="w-3 h-3 bg-amber-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-3 h-3 bg-amber-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
        <div className="container mx-auto px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-1 sm:gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm sm:text-base"
              >
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="hidden sm:inline">العودة للوحة التحكم</span>
                <span className="sm:hidden">العودة</span>
              </Link>
            </div>
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="relative w-8 h-8 sm:w-10 sm:h-10 rounded-xl overflow-hidden">
                <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" />
              </div>
              <div>
                <h1 className="text-base sm:text-lg font-bold text-slate-900 dark:text-white">مشورة</h1>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-4 sm:py-6 lg:py-8">
        <div className="max-w-4xl mx-auto">
          {!examStarted ? (
            /* Exam Introduction */
            <Card className="text-center overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-950/20 dark:to-orange-950/20"></div>
              <CardHeader className="relative p-4 sm:p-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 lg:mb-8 shadow-xl">
                  <Award className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-white" />
                </div>
                <CardTitle className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4 lg:mb-6">
                  الاختبار النهائي
                </CardTitle>
                <h2 className="text-lg sm:text-xl lg:text-2xl text-slate-600 dark:text-slate-400 mb-2 sm:mb-3 lg:mb-4">
                  كورس الإعداد للزواج
                </h2>
                <p className="text-sm sm:text-base lg:text-lg text-slate-500 dark:text-slate-500">
                  اختبارك الأخير للحصول على الشهادة
                </p>
              </CardHeader>
              <CardContent className="space-y-6 sm:space-y-8 lg:space-y-10 relative p-4 sm:p-6">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                  <div className="text-center p-4 sm:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                    <Clock className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-semibold text-slate-900 dark:text-white text-base sm:text-lg">المدة المحددة</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-lg sm:text-xl font-bold">
                      {mockFinalExam.timeLimit} دقيقة
                    </p>
                  </div>
                  <div className="text-center p-4 sm:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                    <Target className="w-8 h-8 sm:w-10 sm:h-10 text-green-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-semibold text-slate-900 dark:text-white text-base sm:text-lg">درجة النجاح</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-lg sm:text-xl font-bold">
                      {mockFinalExam.passingScore}%
                    </p>
                  </div>
                  <div className="text-center p-4 sm:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                    <FileText className="w-8 h-8 sm:w-10 sm:h-10 text-amber-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-semibold text-slate-900 dark:text-white text-base sm:text-lg">عدد الأسئلة</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-lg sm:text-xl font-bold">
                      {mockFinalExam.questions.length} سؤال
                    </p>
                  </div>
                </div>

                <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-4 sm:p-6 lg:p-8 border border-amber-200 dark:border-amber-800 shadow-lg">
                  <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                    <AlertCircle className="w-6 h-6 sm:w-8 sm:h-8 text-amber-600 flex-shrink-0" />
                    <h3 className="font-bold text-slate-900 dark:text-white text-lg sm:text-xl">تعليمات مهمة</h3>
                  </div>
                  <ul className="text-slate-600 dark:text-slate-400 space-y-3 sm:space-y-4 text-right text-sm sm:text-base">
                    <li className="flex items-start gap-2 sm:gap-3">
                      <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>هذا هو الاختبار النهائي للكورس - يجب النجاح للحصول على الشهادة</span>
                    </li>
                    <li className="flex items-start gap-2 sm:gap-3">
                      <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>اقرأ كل سؤال بعناية قبل الإجابة</span>
                    </li>
                    <li className="flex items-start gap-2 sm:gap-3">
                      <RotateCcw className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>يمكنك العودة لتعديل إجاباتك قبل التسليم</span>
                    </li>
                    <li className="flex items-start gap-2 sm:gap-3">
                      <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>سيتم تسليم الاختبار تلقائياً عند انتهاء الوقت</span>
                    </li>
                    <li className="flex items-start gap-2 sm:gap-3">
                      <Trophy className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>يجب الحصول على {mockFinalExam.passingScore}% على الأقل للنجاح</span>
                    </li>
                    <li className="flex items-start gap-2 sm:gap-3">
                      <Award className="w-4 h-4 sm:w-5 sm:h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      <span>يمكنك إعادة الاختبار مرتين إضافيتين في حالة عدم النجاح</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 sm:p-6 lg:p-8 border border-blue-200 dark:border-blue-800 shadow-lg">
                  <h3 className="font-bold text-slate-900 dark:text-white mb-4 sm:mb-6 text-lg sm:text-xl flex items-center gap-2 sm:gap-3">
                    <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                    محتوى الاختبار
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-slate-600 dark:text-slate-400 text-sm sm:text-base">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>سر الزيجة في المسيحية</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>التواصل الفعال</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>الإدارة المالية</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>العلاقة الحميمة والاحترام</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>التعامل مع الأهل</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>التخطيط للمستقبل</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>الحياة الروحية المشتركة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                      <span>حل النزاعات</span>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={handleStartExam}
                  size="lg"
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 sm:px-12 lg:px-16 py-3 sm:py-4 lg:py-5 text-base sm:text-lg lg:text-xl rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                >
                  ابدأ الاختبار النهائي
                  <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-4" />
                </Button>
              </CardContent>
            </Card>
          ) : showResults ? (
            /* Enhanced Final Exam Results */
            <div className="space-y-6 sm:space-y-8">
              {/* Main Result Card */}
              <Card className="text-center overflow-hidden relative">
                <div
                  className={`absolute inset-0 ${
                    score >= mockFinalExam.passingScore
                      ? "bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20"
                      : "bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-950/20 dark:to-orange-950/20"
                  }`}
                ></div>
                <CardHeader className="relative p-4 sm:p-6">
                  <div
                    className={`w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 lg:mb-8 shadow-2xl bg-gradient-to-r ${getPerformanceLevel(score).bgColor}`}
                  >
                    {(() => {
                      const { icon: Icon } = getPerformanceLevel(score)
                      return <Icon className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 text-white" />
                    })()}
                  </div>
                  <CardTitle className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-slate-900 dark:text-white mb-4 sm:mb-6">
                    {score >= mockFinalExam.passingScore ? "مبروك! لقد نجحت" : "لم تنجح هذه المرة"}
                  </CardTitle>
                  <div className="space-y-3 sm:space-y-4">
                    <Badge
                      className={`px-4 sm:px-6 py-2 sm:py-3 text-base sm:text-lg font-bold ${
                        score >= mockFinalExam.passingScore
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200"
                          : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-200"
                      }`}
                    >
                      {getPerformanceLevel(score).level}
                    </Badge>
                    <p className="text-base sm:text-lg lg:text-xl text-slate-600 dark:text-slate-400 px-4">
                      {getPerformanceLevel(score).message}
                    </p>
                    <div
                      className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 ${getPerformanceLevel(score).color}`}
                    >
                      {score}%
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6 sm:space-y-8 lg:space-y-10 relative p-4 sm:p-6">
                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                    <div className="text-center p-3 sm:p-4 lg:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                      <div
                        className={`text-2xl sm:text-3xl lg:text-4xl font-bold mb-2 sm:mb-3 ${getPerformanceLevel(score).color}`}
                      >
                        {score}%
                      </div>
                      <p className="text-slate-600 dark:text-slate-400 font-medium text-xs sm:text-sm lg:text-base">
                        النتيجة النهائية
                      </p>
                    </div>
                    <div className="text-center p-3 sm:p-4 lg:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-green-600 mb-2 sm:mb-3">
                        {correctAnswersCount}
                      </div>
                      <p className="text-slate-600 dark:text-slate-400 font-medium text-xs sm:text-sm lg:text-base">
                        إجابات صحيحة
                      </p>
                    </div>
                    <div className="text-center p-3 sm:p-4 lg:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-red-600 mb-2 sm:mb-3">
                        {mockFinalExam.questions.length - correctAnswersCount}
                      </div>
                      <p className="text-slate-600 dark:text-slate-400 font-medium text-xs sm:text-sm lg:text-base">
                        إجابات خاطئة
                      </p>
                    </div>
                    <div className="text-center p-3 sm:p-4 lg:p-6 bg-white/50 dark:bg-slate-800/50 rounded-xl backdrop-blur-sm shadow-lg">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 dark:text-white mb-2 sm:mb-3">
                        {mockFinalExam.questions.length}
                      </div>
                      <p className="text-slate-600 dark:text-slate-400 font-medium text-xs sm:text-sm lg:text-base">
                        إجمالي الأسئلة
                      </p>
                    </div>
                  </div>

                  {/* Progress Visualization */}
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex justify-between text-base sm:text-lg font-medium text-slate-600 dark:text-slate-400">
                      <span>التقدم الإجمالي</span>
                      <span>{score}%</span>
                    </div>
                    <Progress value={score} className="h-3 sm:h-4" />
                    <div className="flex justify-between text-xs sm:text-sm text-slate-500 dark:text-slate-500">
                      <span>0%</span>
                      <span className="text-amber-600 font-medium">{mockFinalExam.passingScore}% (الحد الأدنى)</span>
                      <span>100%</span>
                    </div>
                  </div>

                  {/* Certificate Eligibility */}
                  {score >= mockFinalExam.passingScore && (
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 sm:p-6 lg:p-8 border border-green-200 dark:border-green-800 shadow-lg">
                      <div className="flex items-center justify-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                        <Award className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
                        <h3 className="font-bold text-green-800 dark:text-green-200 text-lg sm:text-xl lg:text-2xl">
                          مؤهل للحصول على الشهادة!
                        </h3>
                      </div>
                      <p className="text-green-700 dark:text-green-300 mb-4 sm:mb-6 text-sm sm:text-base lg:text-lg text-center">
                        تهانينا! لقد أتممت كورس الإعداد للزواج بنجاح. يمكنك الآن تحميل شهادتك الرسمية.
                      </p>
                      <div className="flex justify-center">
                        <Link href="/certificate">
                          <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-base sm:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto">
                            <Award className="w-5 h-5 sm:w-6 sm:h-6 ml-2 sm:ml-3" />
                            تحميل الشهادة
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
                    {score >= mockFinalExam.passingScore ? (
                      <>
                        <Link href="/dashboard" className="w-full sm:w-auto">
                          <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-base sm:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full">
                            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 ml-2 sm:ml-3" />
                            العودة للوحة التحكم
                          </Button>
                        </Link>
                        <Button
                          onClick={handleRetakeExam}
                          variant="outline"
                          className="border-green-300 text-green-700 hover:bg-green-50 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/20 px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-base sm:text-lg rounded-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto bg-transparent"
                        >
                          <Star className="w-5 h-5 sm:w-6 sm:h-6 ml-2 sm:ml-3" />
                          تحسين النتيجة
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          onClick={handleRetakeExam}
                          className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-base sm:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          <RotateCcw className="w-5 h-5 sm:w-6 sm:h-6 ml-2 sm:ml-3" />
                          إعادة المحاولة
                        </Button>
                        <Link href="/dashboard" className="w-full sm:w-auto">
                          <Button
                            variant="outline"
                            className="border-slate-300 text-slate-700 hover:bg-slate-50 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800 px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-base sm:text-lg rounded-xl transition-all duration-300 hover:scale-105 w-full bg-transparent"
                          >
                            العودة للوحة التحكم
                          </Button>
                        </Link>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Detailed Results */}
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle className="text-xl sm:text-2xl text-slate-900 dark:text-white text-right flex items-center gap-3 sm:gap-4">
                    <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" />
                    تفاصيل الإجابات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                  <div className="max-h-80 sm:max-h-96 overflow-y-auto space-y-3 sm:space-y-4">
                    {mockFinalExam.questions.map((question, index) => {
                      const isCorrect = selectedAnswers[index] === question.correctAnswer
                      return (
                        <Card
                          key={question.id}
                          className={`text-right transition-all duration-300 hover:shadow-lg ${
                            isCorrect
                              ? "border-green-200 dark:border-green-800 bg-green-50/30 dark:bg-green-900/10"
                              : "border-red-200 dark:border-red-800 bg-red-50/30 dark:bg-red-900/10"
                          }`}
                        >
                          <CardContent className="p-4 sm:p-6">
                            <div className="flex items-start gap-3 sm:gap-4">
                              <div
                                className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                                  isCorrect ? "bg-green-100 dark:bg-green-900/30" : "bg-red-100 dark:bg-red-900/30"
                                }`}
                              >
                                {isCorrect ? (
                                  <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
                                ) : (
                                  <X className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" />
                                )}
                              </div>
                              <div className="flex-1 space-y-3 sm:space-y-4">
                                <h4 className="font-bold text-slate-900 dark:text-white text-base sm:text-lg">
                                  السؤال {index + 1}: {question.question}
                                </h4>
                                <div className="space-y-2 sm:space-y-3">
                                  <p className="text-slate-600 dark:text-slate-400 text-sm sm:text-base">
                                    <span className="font-semibold">إجابتك:</span>{" "}
                                    {question.options[selectedAnswers[index]] || "لم تجب"}
                                  </p>
                                  {!isCorrect && (
                                    <p className="text-green-600 dark:text-green-400 text-sm sm:text-base">
                                      <span className="font-semibold">الإجابة الصحيحة:</span>{" "}
                                      {question.options[question.correctAnswer]}
                                    </p>
                                  )}
                                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 sm:p-4 border border-blue-200 dark:border-blue-800">
                                    <p className="text-blue-800 dark:text-blue-200 text-sm sm:text-base">
                                      <span className="font-semibold">التفسير:</span> {question.explanation}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            /* Exam Questions */
            <div className="space-y-4 sm:space-y-6">
              {/* Exam Header */}
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
                    <div className="flex flex-wrap items-center gap-2 sm:gap-4">
                      <Badge className="bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-sm sm:text-base lg:text-lg">
                        السؤال {currentQuestion + 1} من {mockFinalExam.questions.length}
                      </Badge>
                      {timeLeft !== null && (
                        <Badge
                          variant="outline"
                          className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 text-sm sm:text-base lg:text-lg ${
                            timeLeft < 600
                              ? "border-red-300 text-red-600 animate-pulse"
                              : timeLeft < 1800
                                ? "border-amber-300 text-amber-600"
                                : ""
                          }`}
                        >
                          <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                          {formatTime(timeLeft)}
                          {timeLeft < 300 && <span className="text-xs font-bold">(عاجل!)</span>}
                          {timeLeft >= 300 && timeLeft < 600 && <span className="text-xs">(تحذير)</span>}
                        </Badge>
                      )}
                      {unansweredQuestions > 0 && (
                        <Badge
                          variant="outline"
                          className="border-orange-300 text-orange-600 px-3 sm:px-4 py-2 text-xs sm:text-sm"
                        >
                          {unansweredQuestions} سؤال لم يتم الإجابة عليه
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Progress
                    value={((currentQuestion + 1) / mockFinalExam.questions.length) * 100}
                    className="h-2 sm:h-3"
                  />
                </CardContent>
              </Card>

              {/* Enhanced Current Question */}
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                    <CardTitle className="text-lg sm:text-xl lg:text-2xl text-slate-900 dark:text-white text-right leading-relaxed">
                      {mockFinalExam.questions[currentQuestion].question}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge
                        className={`text-xs px-2 py-1 ${getDifficultyColor(getQuestionDifficulty(currentQuestion))}`}
                      >
                        {getQuestionDifficulty(currentQuestion)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {currentQuestion + 1}/{mockFinalExam.questions.length}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                  {mockFinalExam.questions[currentQuestion].options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleAnswerSelect(index)}
                      disabled={!canSubmit}
                      className={`w-full p-4 sm:p-6 text-right rounded-xl border-2 transition-all duration-200 hover:scale-[1.02] disabled:cursor-not-allowed disabled:opacity-50 ${
                        selectedAnswers[currentQuestion] === index
                          ? "border-amber-500 bg-amber-50 dark:bg-amber-900/20 text-amber-900 dark:text-amber-100 shadow-lg"
                          : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white hover:shadow-md"
                      }`}
                    >
                      <div className="flex items-center gap-3 sm:gap-4">
                        <div
                          className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 flex-shrink-0 ${
                            selectedAnswers[currentQuestion] === index
                              ? "border-amber-500 bg-amber-500 scale-110"
                              : "border-slate-300 dark:border-slate-600"
                          }`}
                        >
                          {selectedAnswers[currentQuestion] === index && (
                            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
                          )}
                        </div>
                        <span className="flex-1 text-sm sm:text-base lg:text-lg leading-relaxed">{option}</span>
                      </div>
                    </button>
                  ))}

                  {/* Enhanced Confidence Level & Flag Options */}
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mt-6 p-4 sm:p-6 bg-slate-50 dark:bg-slate-800/50 rounded-xl">
                    <div className="flex flex-col gap-3 w-full lg:w-auto">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        مستوى الثقة في الإجابة:
                      </span>
                      <div className="flex gap-2">
                        {[1, 2, 3, 4, 5].map((level) => (
                          <button
                            key={level}
                            onClick={() => handleConfidenceSelect(level)}
                            disabled={!canSubmit}
                            className={`w-10 h-10 rounded-full border-2 text-sm font-bold transition-all duration-200 hover:scale-110 ${
                              confidenceLevels[currentQuestion] === level
                                ? "border-amber-500 bg-amber-500 text-white shadow-lg"
                                : "border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:border-amber-400"
                            }`}
                          >
                            {level}
                          </button>
                        ))}
                      </div>
                      <span className="text-xs text-slate-500 dark:text-slate-400">1 = غير متأكد، 5 = متأكد جداً</span>
                    </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full lg:w-auto">
                      <Button
                        onClick={() => handleFlagQuestion(currentQuestion)}
                        disabled={!canSubmit}
                        variant="outline"
                        size="sm"
                        className={`w-full sm:w-auto ${
                          flaggedQuestions.has(currentQuestion)
                            ? "border-amber-500 bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300"
                            : "border-slate-300 text-slate-600 dark:border-slate-600 dark:text-slate-400"
                        }`}
                      >
                        <Flag className="w-4 h-4 ml-2" />
                        {flaggedQuestions.has(currentQuestion) ? "إلغاء العلامة" : "وضع علامة"}
                      </Button>

                      {lastSaved && (
                        <div className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1">
                          <CheckCircle className="w-3 h-3" />
                          <span className="hidden sm:inline">تم الحفظ</span>
                          <span>
                            {new Date(lastSaved).toLocaleTimeString("ar-EG", { hour: "2-digit", minute: "2-digit" })}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Navigation */}
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <Button
                  onClick={handlePreviousQuestion}
                  disabled={currentQuestion === 0 || !canSubmit}
                  variant="outline"
                  className="px-4 sm:px-6 lg:px-8 py-3 sm:py-4 bg-transparent text-sm sm:text-base lg:text-lg transition-all duration-300 hover:scale-105 disabled:hover:scale-100 w-full sm:w-auto order-2 sm:order-1"
                >
                  السؤال السابق
                </Button>

                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto order-1 sm:order-2">
                  {currentQuestion === mockFinalExam.questions.length - 1 ? (
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
                      {!showFinalReview && (
                        <Button
                          onClick={() => setShowFinalReview(true)}
                          disabled={!canSubmit}
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-sm sm:text-base lg:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          <FileText className="w-5 h-5 ml-2" />
                          مراجعة نهائية
                        </Button>
                      )}

                      {(showFinalReview || unansweredQuestions === 0) && !showWarning && (
                        <Button
                          onClick={() => setShowWarning(true)}
                          disabled={!canSubmit}
                          className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-sm sm:text-base lg:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          تسليم الاختبار
                        </Button>
                      )}

                      {(unansweredQuestions === 0 || showWarning) && (
                        <Button
                          onClick={handleSubmitExam}
                          disabled={!canSubmit}
                          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 sm:px-8 lg:px-10 py-3 sm:py-4 text-sm sm:text-base lg:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          {showWarning ? "تأكيد التسليم" : "تسليم الاختبار"}
                        </Button>
                      )}
                    </div>
                  ) : (
                    <Button
                      onClick={handleNextQuestion}
                      disabled={!canSubmit}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 sm:px-6 lg:px-8 py-3 sm:py-4 text-sm sm:text-base lg:text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                    >
                      السؤال التالي
                      <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Final Review Summary */}
              {showFinalReview && (
                <Card className="border-blue-300 bg-blue-50 dark:bg-blue-900/20 shadow-lg">
                  <CardContent className="p-4 sm:p-6 lg:p-8">
                    <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                      <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 flex-shrink-0" />
                      <h3 className="font-bold text-blue-800 dark:text-blue-200 text-lg sm:text-xl">
                        المراجعة النهائية
                      </h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl">
                        <div className="text-2xl font-bold text-green-600 mb-2">
                          {selectedAnswers.filter((a) => a !== -1).length}
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">تم الإجابة</p>
                      </div>
                      <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl">
                        <div className="text-2xl font-bold text-red-600 mb-2">{unansweredQuestions}</div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">لم يتم الإجابة</p>
                      </div>
                      <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl">
                        <div className="text-2xl font-bold text-amber-600 mb-2">{flaggedQuestions.size}</div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">مُعلَّم للمراجعة</p>
                      </div>
                      <div className="text-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-xl">
                        <div className="text-2xl font-bold text-blue-600 mb-2">
                          {confidenceLevels.length > 0
                            ? (
                                confidenceLevels.reduce((a, b) => a + b, 0) /
                                  confidenceLevels.filter((c) => c > 0).length || 0
                              ).toFixed(1)
                            : 0}
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">متوسط الثقة</p>
                      </div>
                    </div>

                    {(unansweredQuestions > 0 || flaggedQuestions.size > 0) && (
                      <div className="space-y-4">
                        {unansweredQuestions > 0 && (
                          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                              أسئلة لم يتم الإجابة عليها:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {selectedAnswers.map((answer, index) =>
                                answer === -1 ? (
                                  <button
                                    key={index}
                                    onClick={() => canSubmit && setCurrentQuestion(index)}
                                    disabled={!canSubmit}
                                    className="px-3 py-1 bg-red-200 dark:bg-red-800 text-red-800 dark:text-red-200 rounded-full text-sm hover:bg-red-300 dark:hover:bg-red-700 transition-colors"
                                  >
                                    السؤال {index + 1}
                                  </button>
                                ) : null,
                              )}
                            </div>
                          </div>
                        )}

                        {flaggedQuestions.size > 0 && (
                          <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
                            <h4 className="font-semibold text-amber-800 dark:text-amber-200 mb-2">
                              الأسئلة المُعلَّمة للمراجعة:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {Array.from(flaggedQuestions).map((questionIndex) => (
                                <button
                                  key={questionIndex}
                                  onClick={() => canSubmit && setCurrentQuestion(questionIndex)}
                                  disabled={!canSubmit}
                                  className="px-3 py-1 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 rounded-full text-sm hover:bg-amber-300 dark:hover:bg-amber-700 transition-colors"
                                >
                                  السؤال {questionIndex + 1}
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-3 mt-6">
                      <Button
                        onClick={() => setShowFinalReview(false)}
                        variant="outline"
                        className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-900/20 px-6 py-3 text-base transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                      >
                        إغلاق المراجعة
                      </Button>
                      {unansweredQuestions === 0 && (
                        <Button
                          onClick={() => setShowWarning(true)}
                          className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 text-base rounded-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                        >
                          جاهز للتسليم
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Enhanced Question Navigation */}
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg sm:text-xl">التنقل السريع</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {flaggedQuestions.size} مُعلَّم
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowQuickNav(!showQuickNav)}
                        className="sm:hidden"
                      >
                        <Menu className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => setShowReviewMode(!showReviewMode)}
                        variant="outline"
                        size="sm"
                        className="text-xs hidden sm:inline-flex"
                      >
                        {showReviewMode ? "إخفاء التفاصيل" : "عرض التفاصيل"}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className={`p-4 sm:p-6 ${showQuickNav ? "block" : "hidden sm:block"}`}>
                  <div className="grid grid-cols-5 sm:grid-cols-8 lg:grid-cols-10 gap-2 sm:gap-3">
                    {mockFinalExam.questions.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => canSubmit && setCurrentQuestion(index)}
                        disabled={!canSubmit}
                        className={`relative w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg border-2 font-bold transition-all duration-200 hover:scale-110 disabled:cursor-not-allowed disabled:opacity-50 text-sm sm:text-base ${
                          index === currentQuestion
                            ? "border-amber-500 bg-amber-500 text-white shadow-lg"
                            : selectedAnswers[index] !== -1
                              ? "border-green-300 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:shadow-md"
                              : "border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:border-slate-400 hover:shadow-sm"
                        }`}
                      >
                        {index + 1}
                        {flaggedQuestions.has(index) && (
                          <Flag className="absolute -top-1 -right-1 w-3 h-3 text-amber-500 fill-current" />
                        )}
                        {confidenceLevels[index] > 0 && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                            {confidenceLevels[index]}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>

                  {/* Enhanced Review Summary */}
                  {showReviewMode && (
                    <div className="mt-6 space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-green-800 dark:text-green-200">
                            تم الإجابة: {selectedAnswers.filter((a) => a !== -1).length}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                          <Flag className="w-4 h-4 text-amber-600" />
                          <span className="text-amber-800 dark:text-amber-200">مُعلَّم: {flaggedQuestions.size}</span>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <Target className="w-4 h-4 text-blue-600" />
                          <span className="text-blue-800 dark:text-blue-200">
                            متوسط الثقة:{" "}
                            {confidenceLevels.length > 0
                              ? (
                                  confidenceLevels.reduce((a, b) => a + b, 0) /
                                    confidenceLevels.filter((c) => c > 0).length || 0
                                ).toFixed(1)
                              : 0}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                          <Award className="w-4 h-4 text-purple-600" />
                          <span className="text-purple-800 dark:text-purple-200">المحاولة: {examAttempts + 1}/3</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex flex-wrap items-center gap-4 sm:gap-6 mt-4 sm:mt-6 text-xs sm:text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 sm:w-4 sm:h-4 bg-amber-500 rounded flex-shrink-0"></div>
                      <span className="text-slate-600 dark:text-slate-400">السؤال الحالي</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 sm:w-4 sm:h-4 bg-green-50 dark:bg-green-900/20 border border-green-300 rounded flex-shrink-0"></div>
                      <span className="text-slate-600 dark:text-slate-400">تم الإجابة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 sm:w-4 sm:h-4 border border-slate-300 dark:border-slate-600 rounded flex-shrink-0"></div>
                      <span className="text-slate-600 dark:text-slate-400">لم يتم الإجابة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Flag className="w-3 h-3 sm:w-4 sm:h-4 text-amber-500 flex-shrink-0" />
                      <span className="text-slate-600 dark:text-slate-400">مُعلَّم</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 sm:w-4 sm:h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center flex-shrink-0">
                        5
                      </div>
                      <span className="text-slate-600 dark:text-slate-400">مستوى الثقة</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
