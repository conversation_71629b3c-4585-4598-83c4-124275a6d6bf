@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  /* High contrast mode */
  .high-contrast {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --border: 0 0% 0%;
  }

  .high-contrast.dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --border: 0 0% 100%;
  }

  /* Font size variations */
  .font-small {
    font-size: 0.875rem;
  }

  .font-medium {
    font-size: 1rem;
  }

  .font-large {
    font-size: 1.125rem;
  }

  .font-extra-large {
    font-size: 1.25rem;
  }
}

* {
  @apply border-border;
}
body {
  @apply bg-background text-foreground;
  font-family: "Cairo", "Inter", system-ui, -apple-system, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus indicators */
*:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Reduced motion */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@layer utilities {
  .font-cairo {
    font-family: "Cairo", "Inter", system-ui, -apple-system, sans-serif;
  }

  /* Animation utilities with reduced motion support */
  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-bounce-slow {
    animation: bounce-slow 2s ease-in-out infinite;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  /* Animation delays */
  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  .animation-delay-800 {
    animation-delay: 800ms;
  }

  .animation-delay-1000 {
    animation-delay: 1000ms;
  }

  .animation-delay-1200 {
    animation-delay: 1200ms;
  }

  .animation-delay-1400 {
    animation-delay: 1400ms;
  }

  .animation-delay-1600 {
    animation-delay: 1600ms;
  }

  /* Glass effect */
  .glass-card {
    @apply backdrop-blur-md bg-white/10 dark:bg-slate-900/20 border border-white/20 dark:border-slate-700/30;
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #f59e0b, #f97316, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Floating elements */
  .floating-element {
    @apply bg-gradient-to-br from-amber-400/20 to-orange-500/20 dark:from-amber-400/10 dark:to-orange-500/10;
  }

  /* Performance optimizations */
  .optimize-rendering {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Responsive utilities */
  @media (max-width: 640px) {
    .animate-fade-in-up {
      animation-duration: 0.6s;
    }
  }
}

/* Keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce-slow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Theme-specific optimizations */
.glass-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.dark .glass-card {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.light .glass-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(251, 191, 36, 0.2);
  box-shadow: 0 8px 32px rgba(251, 191, 36, 0.1);
}

/* Gradient text with performance optimization */
.gradient-text {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  will-change: background-position;
}

.light .gradient-text {
  background: linear-gradient(135deg, #1e293b, #334155, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Optimized scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(15 23 42);
}

.light ::-webkit-scrollbar-track {
  background: rgb(254 252 232);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 4px;
}

.light ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #1e293b, #334155);
  border-radius: 4px;
}

/* Print optimizations */
@media print {
  .print\:hidden {
    display: none !important;
  }

  .print\:block {
    display: block !important;
  }

  * {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  .animate-fade-in-up,
  .animate-scale-in,
  .animate-float,
  .animate-bounce-slow,
  .animate-wiggle,
  .animate-glow,
  .animate-gradient {
    animation: none;
  }

  .glass-card {
    @apply bg-white border border-gray-300;
  }
}

/* Smooth scrolling with performance consideration */
html {
  scroll-behavior: smooth;
}

.reduce-motion html {
  scroll-behavior: auto;
}

/* Critical CSS inlining optimization */
.above-fold {
  contain: layout style;
}

/* Intersection observer optimization */
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* RTL optimizations */
[dir="rtl"] .animate-fade-in-left {
  animation: fadeInRight 0.8s ease-out forwards;
}

[dir="rtl"] .animate-fade-in-right {
  animation: fadeInLeft 0.8s ease-out forwards;
}

/* Theme transition optimization */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease,
    color 0.3s ease;
}

.reduce-motion * {
  transition: none !important;
}

/* Content visibility for performance */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Font loading optimization */
@font-face {
  font-family: "Cairo";
  font-display: swap;
  src: local("Cairo");
}

/* Critical resource hints */
.preload-hint {
  content: "";
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-scale-in,
  .animate-float,
  .animate-bounce-slow,
  .animate-wiggle,
  .animate-glow,
  .animate-gradient {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .high-contrast-auto {
    filter: contrast(150%);
  }
}
