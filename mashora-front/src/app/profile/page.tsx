"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Church,
  Lock,
  Eye,
  EyeOff,
  Save,
  ArrowRight,
  AlertCircle,
  CheckCircle,
  Camera,
  Upload,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock user data
const mockUser = {
  firstName: "أحمد",
  lastName: "محمد",
  email: "<EMAIL>",
  phone: "01012345678",
  nationalId: "12345678901234",
  dateOfBirth: "1995-05-15",
  address: "شارع النيل، المعادي، القاهرة",
  churchBelong: "السيدة العذراء والقديس يوسف النجار - الخصوص",
  fatherOfConfession: "أبونا يوسف",
  confessionChurch: "كنيسة مارجرجس",
  avatar: "/placeholder.svg?height=120&width=120",
}

export default function ProfilePage() {
  const [formData, setFormData] = useState(mockUser)
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  })
  const [isLoading, setIsLoading] = useState(false)
  const [saveStatus, setSaveStatus] = useState<"idle" | "success" | "error">("idle")
  const [activeTab, setActiveTab] = useState<"profile" | "password">("profile")

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSaveProfile = async () => {
    setIsLoading(true)
    setSaveStatus("idle")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setSaveStatus("success")
      setTimeout(() => setSaveStatus("idle"), 3000)
    } catch (error) {
      setSaveStatus("error")
    } finally {
      setIsLoading(false)
    }
  }

  const handleChangePassword = async () => {
    setIsLoading(true)
    setSaveStatus("idle")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setSaveStatus("success")
      setPasswordData({ currentPassword: "", newPassword: "", confirmPassword: "" })
      setTimeout(() => setSaveStatus("idle"), 3000)
    } catch (error) {
      setSaveStatus("error")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <ArrowRight className="w-5 h-5" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative w-10 h-10 rounded-xl overflow-hidden">
                <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900 dark:text-white">مشورة</h1>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">تعديل الملف الشخصي</h1>
            <p className="text-slate-600 dark:text-slate-400">Edit Profile</p>
          </div>

          {/* Status Messages */}
          {saveStatus === "success" && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="text-green-800 dark:text-green-200">تم حفظ التغييرات بنجاح</span>
            </div>
          )}

          {saveStatus === "error" && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
              <span className="text-red-800 dark:text-red-200">حدث خطأ أثناء حفظ التغييرات</span>
            </div>
          )}

          {/* Tab Navigation */}
          <div className="flex gap-2 mb-8">
            <Button
              variant={activeTab === "profile" ? "default" : "outline"}
              onClick={() => setActiveTab("profile")}
              className={`px-6 py-3 ${
                activeTab === "profile"
                  ? "bg-blue-600 text-white"
                  : "bg-transparent border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400"
              }`}
            >
              <User className="w-4 h-4 ml-2" />
              البيانات الشخصية
            </Button>
            <Button
              variant={activeTab === "password" ? "default" : "outline"}
              onClick={() => setActiveTab("password")}
              className={`px-6 py-3 ${
                activeTab === "password"
                  ? "bg-blue-600 text-white"
                  : "bg-transparent border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400"
              }`}
            >
              <Lock className="w-4 h-4 ml-2" />
              تغيير كلمة المرور
            </Button>
          </div>

          {activeTab === "profile" ? (
            <div className="space-y-8">
              {/* Profile Picture */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Camera className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    الصورة الشخصية
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <img
                        src={formData.avatar || "/placeholder.svg"}
                        alt="الصورة الشخصية"
                        className="w-24 h-24 rounded-full object-cover border-4 border-blue-100 dark:border-blue-900"
                      />
                      <button className="absolute -bottom-2 -left-2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors">
                        <Upload className="w-4 h-4" />
                      </button>
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-900 dark:text-white mb-1">
                        {formData.firstName} {formData.lastName}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-400 text-sm mb-3">اضغط على الأيقونة لتغيير الصورة</p>
                      <Badge variant="secondary" className="text-xs">
                        JPG, PNG حتى 5MB
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    البيانات الشخصية
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">الاسم الأول *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange("firstName", e.target.value)}
                        className="text-right"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">الاسم الأخير *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange("lastName", e.target.value)}
                        className="text-right"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-amber-500" />
                      البريد الإلكتروني *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className="text-right"
                      dir="ltr"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone" className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-green-500" />
                      رقم الهاتف *
                    </Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      className="text-right"
                      dir="ltr"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth" className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-purple-500" />
                      تاريخ الميلاد *
                    </Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address" className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-red-500" />
                      العنوان *
                    </Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange("address", e.target.value)}
                      className="text-right min-h-[100px]"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Church Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Church className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    البيانات الكنسية
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="churchBelong">الكنيسة التابع لها *</Label>
                    <Input
                      id="churchBelong"
                      value={formData.churchBelong}
                      onChange={(e) => handleInputChange("churchBelong", e.target.value)}
                      className="text-right"
                      readOnly
                    />
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      لتغيير الكنيسة، يرجى التواصل مع الإدارة
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fatherOfConfession">أب الاعتراف *</Label>
                    <Input
                      id="fatherOfConfession"
                      value={formData.fatherOfConfession}
                      onChange={(e) => handleInputChange("fatherOfConfession", e.target.value)}
                      className="text-right"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confessionChurch">كنيسة أب الاعتراف *</Label>
                    <Input
                      id="confessionChurch"
                      value={formData.confessionChurch}
                      onChange={(e) => handleInputChange("confessionChurch", e.target.value)}
                      className="text-right"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-center">
                <Button
                  onClick={handleSaveProfile}
                  disabled={isLoading}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2" />
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <Save className="w-5 h-5 ml-2" />
                      حفظ التغييرات
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            /* Password Change Tab */
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <Lock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  تغيير كلمة المرور
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">كلمة المرور الحالية *</Label>
                  <div className="relative">
                    <Input
                      id="currentPassword"
                      type={showPasswords.current ? "text" : "password"}
                      value={passwordData.currentPassword}
                      onChange={(e) => handlePasswordChange("currentPassword", e.target.value)}
                      className="text-right pr-12"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => ({ ...prev, current: !prev.current }))}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      {showPasswords.current ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">كلمة المرور الجديدة *</Label>
                  <div className="relative">
                    <Input
                      id="newPassword"
                      type={showPasswords.new ? "text" : "password"}
                      value={passwordData.newPassword}
                      onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                      className="text-right pr-12"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => ({ ...prev, new: !prev.new }))}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      {showPasswords.new ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  <p className="text-xs text-slate-500 dark:text-slate-400">يجب أن تحتوي على 8 أحرف على الأقل</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور الجديدة *</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showPasswords.confirm ? "text" : "password"}
                      value={passwordData.confirmPassword}
                      onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                      className="text-right pr-12"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords((prev) => ({ ...prev, confirm: !prev.confirm }))}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      {showPasswords.confirm ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={handleChangePassword}
                    disabled={isLoading}
                    size="lg"
                    className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    {isLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2" />
                        جاري التحديث...
                      </>
                    ) : (
                      <>
                        <Lock className="w-5 h-5 ml-2" />
                        تحديث كلمة المرور
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  )
}
