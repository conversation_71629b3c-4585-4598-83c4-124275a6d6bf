"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sun, Moon } from "lucide-react"

export function ThemeToggle() {
  const [isDark, setIsDark] = useState(true)

  useEffect(() => {
    // Check for saved theme preference or default to dark
    const savedTheme = localStorage.getItem("theme")
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches

    if (savedTheme) {
      setIsDark(savedTheme === "dark")
    } else {
      setIsDark(prefersDark)
    }
  }, [])

  useEffect(() => {
    // Apply theme to document
    if (isDark) {
      document.documentElement.classList.add("dark")
      document.documentElement.classList.remove("light")
    } else {
      document.documentElement.classList.add("light")
      document.documentElement.classList.remove("dark")
    }

    // Save preference
    localStorage.setItem("theme", isDark ? "dark" : "light")
  }, [isDark])

  const toggleTheme = () => {
    setIsDark(!isDark)
  }

  return (
    <Button
      onClick={toggleTheme}
      variant="outline"
      size="sm"
      className={`relative overflow-hidden transition-all duration-500 ${
        isDark
          ? "border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300"
          : "border-amber-200 bg-amber-50/50 hover:bg-amber-100/50 text-amber-800"
      }`}
    >
      <div className="relative z-10 flex items-center gap-2">
        {isDark ? (
          <>
            <Sun className="w-4 h-4" />
            <span className="hidden sm:inline">فاتح</span>
          </>
        ) : (
          <>
            <Moon className="w-4 h-4" />
            <span className="hidden sm:inline">داكن</span>
          </>
        )}
      </div>

      {/* Animated background */}
      <div
        className={`absolute inset-0 transition-transform duration-500 ${
          isDark
            ? "bg-gradient-to-r from-amber-500/20 to-orange-500/20 translate-x-full"
            : "bg-gradient-to-r from-slate-500/20 to-slate-600/20 -translate-x-full"
        }`}
      />
    </Button>
  )
}
